# mobile pipeline: using React Native

trigger: none

parameters:
  - name: versionNumber
    displayName: Version Number
    type: string
    default: '4.6.0'
  - name: buildNumber
    displayName: Build Number
    type: string
    default: 'null'

resources:
  repositories:
    # Checkmarx SAST
    - repository: AdsTemplatesSecurity
      type: git
      name: DevOps-SRE/ads-ado-templates-security
      ref: refs/tags/2.x
    # Checkmarx SCA
    - repository: dses-sec-templates
      type: git
      name: DS-DSES/dses-sec-templates
    - repository: bp-via-checkmarx
      type: git
      name: Vulnerability_Identification_and_Awareness/bp-via-checkmarx

stages:
  - ${{ if eq(parameters.buildNumber, 'null') }}:
      - template: stages/debug.yml
        parameters:
          applications:
            - mobile_app
          versionNumber: ${{ parameters.versionNumber }}
          buildNumber: $(Build.BuildId)
          environment: performance
  - ${{ if ne(parameters.buildNumber, 'null') }}:
      - template: stages/release.yml
        parameters:
          applications:
            - mobile_app
          versionNumber: ${{ parameters.versionNumber }}
          buildNumber: ${{ parameters.buildNumber }}
          environment: performance
