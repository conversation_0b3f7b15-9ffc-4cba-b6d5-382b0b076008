# mobile pipeline: using React Native

trigger: none

parameters:
  - name: versionNumber
    displayName: Version Number
    type: string
    default: '4.6.0'
  - name: buildNumber
    displayName: Build Number
    type: string
    default: 'null'
  - name: iosAppCenterBuild
    displayName: 'iOS AppCenter Build'
    type: boolean
    default: false
  - name: runMaestro
    displayName: Run maestro
    type: boolean
    default: false
  - name: run_e2e
    displayName: Run E2E
    type: boolean
    default: false
  - name: useADOAgent
    displayName: Use ADO agent (true) or EC2 self hosted (false). False (EC2) for Maestro.
    type: boolean
    default: false
  - name: brand
    displayName: Brand to use
    type: string
    default: bp
    values:
      - bp
      - aral
      - bpUS

resources:
  repositories:
    # Checkmarx SAST
    - repository: AdsTemplatesSecurity
      type: git
      name: DevOps-SRE/ads-ado-templates-security
      ref: refs/tags/2.x
    # Checkmarx SCA
    - repository: dses-sec-templates
      type: git
      name: DS-DSES/dses-sec-templates
    - repository: bp-via-checkmarx
      type: git
      name: Vulnerability_Identification_and_Awareness/bp-via-checkmarx
    - repository: MaestroServer
      type: git
      name: bp_pulse/maestro-server
      ref: main

variables:
  - group: 'bp-pulse-mobile-app-preprod'
  - ${{ if eq(parameters.brand, 'aral') }}:
      - group: 'aral-pulse-mobile-app-preprod'
  - ${{ if eq(parameters.brand, 'bpUS') }}:
      - group: 'us-pulse-mobile-app-preprod'

stages:
  - template: stages/release.yml
    parameters:
      applications:
        - mobile_app
      versionNumber: ${{ parameters.versionNumber }}
      iosAppCenterBuild: ${{ parameters.iosAppCenterBuild }}
      runMaestro: ${{ parameters.runMaestro }}
      run_e2e: ${{ parameters.run_e2e }}
      environment: preprod
      useADOAgent: ${{ parameters.useADOAgent }}
      brand: ${{ parameters.brand }}
      ${{ if eq(parameters.buildNumber, 'null') }}:
        buildNumber: $(Build.BuildId)
      ${{ if ne(parameters.buildNumber, 'null') }}:
        buildNumber: ${{ parameters.buildNumber }}
