# mobile pipeline: using React Native

trigger: none

resources:
  repositories:
    # Checkmarx SAST
    - repository: AdsTemplatesSecurity
      type: git
      name: DevOps-SRE/ads-ado-templates-security
      ref: refs/tags/2.x
    # Checkmarx SCA
    - repository: dses-sec-templates
      type: git
      name: DS-DSES/dses-sec-templates
    - repository: bp-via-checkmarx
      type: git
      name: Vulnerability_Identification_and_Awareness/bp-via-checkmarx

parameters:
  - name: versionNumber
    displayName: Version Number
    type: string
    default: '4.6.0'
  - name: buildNumber
    displayName: Build Number
    type: string
    default: 'null'
  - name: useADOAgent
    displayName: Use ADO agent (true) or EC2 self hosted (false). False (EC2) for Maestro.
    type: boolean
    default: false
  - name: brand
    displayName: Brand to use
    type: string
    default: bp
    values:
      - bp
      - aral
      - bpUS

variables:
  - group: 'bp-pulse-mobile-app-prod'
  - ${{ if eq(parameters.brand, 'aral') }}:
      - group: 'aral-pulse-mobile-app-prod'
  - ${{ if eq(parameters.brand, 'bpUS') }}:
      - group: 'us-pulse-mobile-app-prod'

stages:
  - template: stages/release.yml
    parameters:
      applications:
        - mobile_app
      versionNumber: ${{ parameters.versionNumber }}
      environment: prod
      brand: ${{ parameters.brand }}
      useADOAgent: ${{ parameters.useADOAgent }}
      ${{ if eq(parameters.buildNumber, 'null') }}:
        buildNumber: $(Build.BuildId)
        iosAppCenterBuild: false
      ${{ if ne(parameters.buildNumber, 'null') }}:
        buildNumber: ${{ parameters.buildNumber }}
        iosAppCenterBuild: ${{ parameters.iosAppCenterBuild }}
