# iOS Notification Service Extension for Airship

This Notification Service Extension enables rich push notifications with image support for the bp pulse app using Airship SDK.

## Overview

The Notification Service Extension allows the app to:
- Download and display images in push notifications
- Process rich media content (images, videos, etc.)
- Provide enhanced notification analytics through Airship
- Handle notification modifications before display

## Implementation Details

### Files Structure
```
NotificationServiceExtension/
├── NotificationService.swift          # Main extension implementation
├── Info.plist                        # Extension configuration
├── NotificationServiceExtension.entitlements  # App groups configuration
└── README.md                         # This documentation
```

### Key Components

#### 1. NotificationService.swift
- Integrates with Airship's `UANotificationServiceExtension`
- Automatically handles Airship notifications for rich media
- Falls back to standard processing for non-Airship notifications

#### 2. App Groups Configuration
- Enables data sharing between main app and extension
- App group: `group.com.aml.ev-app.bppulse`
- Required for Airship analytics and shared preferences

#### 3. Entitlements
All app targets include the same app group:
- `bppulse.entitlements`
- `bppulse.debug.entitlements`
- `bppulse.prod.entitlements`
- `aralpulse.entitlements`
- `bpUSpulse.entitlements`

## How It Works

1. **Push Notification Received**: iOS delivers the notification to the extension
2. **Airship Detection**: Extension checks if it's an Airship notification
3. **Rich Media Processing**: If Airship notification, downloads images/media
4. **Content Modification**: Airship modifies notification content with rich media
5. **Display**: Enhanced notification is displayed to the user

## Testing Rich Notifications

### From Airship Dashboard
1. Create a new push notification campaign
2. Add an image URL in the "Rich Media" section
3. Send to your test device
4. Verify image appears in the notification

### Sample Payload
```json
{
  "aps": {
    "alert": {
      "title": "bp pulse",
      "body": "Your charging session is complete!"
    },
    "mutable-content": 1
  },
  "com.urbanairship.image": "https://example.com/notification-image.jpg"
}
```

## Troubleshooting

### Common Issues

1. **Images not loading**
   - Verify `mutable-content: 1` is set in the payload
   - Check image URL is accessible and valid
   - Ensure app groups are properly configured

2. **Extension not running**
   - Verify the extension target is included in the build
   - Check that AirshipServiceExtension pod is installed
   - Ensure proper code signing and provisioning

3. **App groups not working**
   - Verify all entitlements files include the same app group
   - Check Apple Developer Console for app group configuration
   - Ensure provisioning profiles include app groups capability

### Debug Tips

1. **Xcode Console**: Check for extension logs during notification delivery
2. **Device Console**: Use Console.app to view system logs
3. **Airship Analytics**: Verify notification events in Airship dashboard

## Requirements

- iOS 15.0+
- Airship SDK 19.1.2+
- AirshipServiceExtension pod
- Proper app groups configuration
- Valid push notification certificates

## Security Considerations

- Extension runs in a sandboxed environment
- Limited execution time (30 seconds max)
- Network requests should be minimal and efficient
- Sensitive data should not be processed in the extension
