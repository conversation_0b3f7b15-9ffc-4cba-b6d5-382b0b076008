//
//  NotificationService.swift
//  NotificationServiceExtension
//
//  Created by <PERSON><PERSON><PERSON> on 18/06/25.
//

import UserNotifications
import AirshipServiceExtension

class NotificationService: UNNotificationServiceExtension {

    var contentHandler: ((UNNotificationContent) -> Void)?
    var bestAttemptContent: UNMutableNotificationContent?

    override func didReceive(_ request: UNNotificationRequest, withContentHandler contentHandler: @escaping (UNNotificationContent) -> Void) {
        self.contentHandler = contentHandler
        bestAttemptContent = (request.content.mutableCopy() as? UNMutableNotificationContent)

        // Check if this is an Airship notification
        if UANotificationServiceExtension.isAirshipNotification(request) {
            // Let Airship handle the notification (for rich media, analytics, etc.)
            UANotificationServiceExtension.handleNotificationRequest(
                request,
                withContentHandler: contentHandler
            )
        } else {
            // Handle non-Airship notifications
            if let bestAttemptContent = bestAttemptContent {
                // Apply any custom modifications for non-Airship notifications
                contentHandler(bestAttemptContent)
            }
        }
    }

    override func serviceExtensionTimeWillExpire() {
        // Called just before the extension will be terminated by the system.
        // Use this as an opportunity to deliver your "best attempt" at modified content, otherwise the original push payload will be used.
        if let contentHandler = contentHandler, let bestAttemptContent = bestAttemptContent {
            contentHandler(bestAttemptContent)
        }
    }

}
