#!/bin/bash

# Script to fix PBXFileSystemSynchronizedRootGroup issue in Xcode project
# This removes the incompatible NotificationServiceExtension entries

set -e

echo "🔧 Fixing Xcode project compatibility issue"
echo "=========================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

PROJECT_FILE="bppulse.xcodeproj/project.pbxproj"

# Check if project file exists
if [ ! -f "$PROJECT_FILE" ]; then
    echo -e "${RED}❌ Error: $PROJECT_FILE not found${NC}"
    exit 1
fi

echo -e "${BLUE}📋 Step 1: Creating backup of project file...${NC}"
cp "$PROJECT_FILE" "$PROJECT_FILE.backup.$(date +%Y%m%d_%H%M%S)"
echo -e "${GREEN}✅ Backup created${NC}"

echo -e "${BLUE}📋 Step 2: Removing PBXFileSystemSynchronizedRootGroup entries...${NC}"

# Remove the problematic PBXFileSystemSynchronizedRootGroup entry
sed -i '' '/PBXFileSystemSynchronizedRootGroup/d' "$PROJECT_FILE"

# Remove any lines containing NotificationServiceExtension with PBXFileSystemSynchronizedRootGroup
sed -i '' '/NotificationServiceExtension.*PBXFileSystemSynchronizedRootGroup/d' "$PROJECT_FILE"

# Remove specific UUID references that might be causing issues
sed -i '' '/1DE455602E00561B00035531/d' "$PROJECT_FILE"

# Remove any remaining NotificationServiceExtension references that might be problematic
# But keep the basic ones that might be needed
grep -n "NotificationServiceExtension" "$PROJECT_FILE" | head -5

echo -e "${GREEN}✅ Problematic entries removed${NC}"

echo -e "${BLUE}📋 Step 3: Cleaning up any orphaned references...${NC}"

# Remove any empty groups or orphaned references
sed -i '' '/^[[:space:]]*$/d' "$PROJECT_FILE"

echo -e "${GREEN}✅ Cleanup completed${NC}"

echo -e "${BLUE}📋 Step 4: Verifying project file integrity...${NC}"

# Check if the file is still valid
if grep -q "PBXFileSystemSynchronizedRootGroup" "$PROJECT_FILE"; then
    echo -e "${YELLOW}⚠️  Some PBXFileSystemSynchronizedRootGroup entries may still exist${NC}"
else
    echo -e "${GREEN}✅ All PBXFileSystemSynchronizedRootGroup entries removed${NC}"
fi

# Count remaining NotificationServiceExtension references
REMAINING_REFS=$(grep -c "NotificationServiceExtension" "$PROJECT_FILE" || echo "0")
echo -e "${BLUE}📊 Remaining NotificationServiceExtension references: $REMAINING_REFS${NC}"

echo ""
echo -e "${GREEN}🎉 Project file fix completed!${NC}"
echo ""
echo -e "${YELLOW}📝 NEXT STEPS:${NC}"
echo "1. Try running: pod install"
echo "2. If successful, you can recreate the extension target properly"
echo "3. If issues persist, restore from backup and try alternative approach"
echo ""
echo -e "${BLUE}📖 Backup location:${NC}"
ls -la "$PROJECT_FILE.backup."*
