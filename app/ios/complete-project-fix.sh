#!/bin/bash

# Complete fix for Xcode project compatibility issues
# This removes all NotificationServiceExtension references and problematic entries

set -e

echo "🔧 Complete Xcode project fix for CocoaPods compatibility"
echo "======================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

PROJECT_FILE="bppulse.xcodeproj/project.pbxproj"

# Check if project file exists
if [ ! -f "$PROJECT_FILE" ]; then
    echo -e "${RED}❌ Error: $PROJECT_FILE not found${NC}"
    exit 1
fi

echo -e "${BLUE}📋 Step 1: Creating backup of project file...${NC}"
cp "$PROJECT_FILE" "$PROJECT_FILE.backup.complete.$(date +%Y%m%d_%H%M%S)"
echo -e "${GREEN}✅ Backup created${NC}"

echo -e "${BLUE}📋 Step 2: Removing all NotificationServiceExtension references...${NC}"

# Remove all lines containing NotificationServiceExtension
sed -i '' '/NotificationServiceExtension/d' "$PROJECT_FILE"

echo -e "${GREEN}✅ NotificationServiceExtension references removed${NC}"

echo -e "${BLUE}📋 Step 3: Removing problematic UUIDs...${NC}"

# Remove specific UUIDs that are causing issues
PROBLEMATIC_UUIDS=(
    "1DE455512E00561B00035531"
    "1DE4556D2E0064FA00035531" 
    "1DE4556C2E0064F900035531"
    "1DE455732E0064FA00035531"
    "1DE4556B2E0064F900035531"
    "1DE455602E00561B00035531"
)

for uuid in "${PROBLEMATIC_UUIDS[@]}"; do
    sed -i '' "/$uuid/d" "$PROJECT_FILE"
    echo -e "${GREEN}✅ Removed UUID: $uuid${NC}"
done

echo -e "${BLUE}📋 Step 4: Removing fileSystemSynchronizedGroups references...${NC}"

# Remove fileSystemSynchronizedGroups entries
sed -i '' '/fileSystemSynchronizedGroups/d' "$PROJECT_FILE"

echo -e "${GREEN}✅ fileSystemSynchronizedGroups references removed${NC}"

echo -e "${BLUE}📋 Step 5: Fixing object version compatibility...${NC}"

# Check current object version
CURRENT_VERSION=$(grep "objectVersion" "$PROJECT_FILE" | head -1 | sed 's/.*objectVersion = \([0-9]*\);.*/\1/')
echo -e "${BLUE}📊 Current object version: $CURRENT_VERSION${NC}"

if [ "$CURRENT_VERSION" -gt "56" ]; then
    echo -e "${YELLOW}⚠️  Object version $CURRENT_VERSION may be too new for CocoaPods${NC}"
    echo -e "${BLUE}📋 Setting object version to 56 for compatibility...${NC}"
    sed -i '' 's/objectVersion = [0-9]*;/objectVersion = 56;/' "$PROJECT_FILE"
    echo -e "${GREEN}✅ Object version set to 56${NC}"
fi

echo -e "${BLUE}📋 Step 6: Cleaning up empty lines and formatting...${NC}"

# Remove empty lines and clean up formatting
sed -i '' '/^[[:space:]]*$/d' "$PROJECT_FILE"

echo -e "${GREEN}✅ Cleanup completed${NC}"

echo -e "${BLUE}📋 Step 7: Verifying project file integrity...${NC}"

# Verify no problematic entries remain
if grep -q "NotificationServiceExtension" "$PROJECT_FILE"; then
    echo -e "${RED}❌ Some NotificationServiceExtension references still exist${NC}"
    exit 1
else
    echo -e "${GREEN}✅ All NotificationServiceExtension references removed${NC}"
fi

if grep -q "fileSystemSynchronizedGroups" "$PROJECT_FILE"; then
    echo -e "${RED}❌ Some fileSystemSynchronizedGroups references still exist${NC}"
    exit 1
else
    echo -e "${GREEN}✅ All fileSystemSynchronizedGroups references removed${NC}"
fi

# Check object version
NEW_VERSION=$(grep "objectVersion" "$PROJECT_FILE" | head -1 | sed 's/.*objectVersion = \([0-9]*\);.*/\1/')
echo -e "${BLUE}📊 Final object version: $NEW_VERSION${NC}"

echo ""
echo -e "${GREEN}🎉 Complete project fix completed successfully!${NC}"
echo ""
echo -e "${YELLOW}📝 WHAT WAS FIXED:${NC}"
echo "• Removed all NotificationServiceExtension references"
echo "• Removed problematic UUIDs causing CocoaPods errors"
echo "• Removed fileSystemSynchronizedGroups entries"
echo "• Set compatible object version (56)"
echo "• Cleaned up project file formatting"
echo ""
echo -e "${YELLOW}📝 NEXT STEPS:${NC}"
echo "1. Try running: pod install"
echo "2. If successful, you can recreate the extension target from scratch"
echo "3. Use legacy Xcode project format when recreating"
echo ""
echo -e "${BLUE}📖 Backup location:${NC}"
ls -la "$PROJECT_FILE.backup.complete."*
