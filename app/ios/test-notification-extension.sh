#!/bin/bash

# Test script for iOS Notification Service Extension
# This script helps verify the extension is properly configured

set -e

echo "🔍 Testing iOS Notification Service Extension Configuration"
echo "========================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Check if we're in the right directory
if [ ! -f "Podfile" ]; then
    echo -e "${RED}❌ Error: Please run this script from the ios directory${NC}"
    exit 1
fi

echo -e "${BLUE}📋 Step 1: Checking Podfile configuration...${NC}"
if grep -q "AirshipServiceExtension" Podfile; then
    echo -e "${GREEN}✅ AirshipServiceExtension pod found in Podfile${NC}"
else
    echo -e "${RED}❌ AirshipServiceExtension pod not found in Podfile${NC}"
    exit 1
fi

echo -e "${BLUE}📋 Step 2: Checking NotificationServiceExtension files...${NC}"
if [ -f "NotificationServiceExtension/NotificationService.swift" ]; then
    echo -e "${GREEN}✅ NotificationService.swift exists${NC}"
else
    echo -e "${RED}❌ NotificationService.swift not found${NC}"
    exit 1
fi

if [ -f "NotificationServiceExtension/Info.plist" ]; then
    echo -e "${GREEN}✅ Info.plist exists${NC}"
else
    echo -e "${RED}❌ Info.plist not found${NC}"
    exit 1
fi

if [ -f "NotificationServiceExtension/NotificationServiceExtension.entitlements" ]; then
    echo -e "${GREEN}✅ Entitlements file exists${NC}"
else
    echo -e "${RED}❌ Entitlements file not found${NC}"
    exit 1
fi

echo -e "${BLUE}📋 Step 3: Checking app groups in entitlements...${NC}"
APP_GROUP="group.com.aml.ev-app.bppulse"

# Check main app entitlements
for entitlements_file in bppulse/bppulse.entitlements bppulse/bppulse.debug.entitlements bppulse/bppulse.prod.entitlements aralpulse/aralpulse.entitlements bpUSpulse/bpUSpulse.entitlements; do
    if [ -f "$entitlements_file" ]; then
        if grep -q "$APP_GROUP" "$entitlements_file"; then
            echo -e "${GREEN}✅ App group found in $entitlements_file${NC}"
        else
            echo -e "${YELLOW}⚠️  App group not found in $entitlements_file${NC}"
        fi
    else
        echo -e "${YELLOW}⚠️  $entitlements_file not found${NC}"
    fi
done

# Check extension entitlements
if grep -q "$APP_GROUP" "NotificationServiceExtension/NotificationServiceExtension.entitlements"; then
    echo -e "${GREEN}✅ App group found in extension entitlements${NC}"
else
    echo -e "${RED}❌ App group not found in extension entitlements${NC}"
    exit 1
fi

echo -e "${BLUE}📋 Step 4: Checking Swift implementation...${NC}"
if grep -q "import AirshipServiceExtension" "NotificationServiceExtension/NotificationService.swift"; then
    echo -e "${GREEN}✅ AirshipServiceExtension import found${NC}"
else
    echo -e "${RED}❌ AirshipServiceExtension import not found${NC}"
    exit 1
fi

if grep -q "UANotificationServiceExtension" "NotificationServiceExtension/NotificationService.swift"; then
    echo -e "${GREEN}✅ UANotificationServiceExtension usage found${NC}"
else
    echo -e "${RED}❌ UANotificationServiceExtension usage not found${NC}"
    exit 1
fi

echo -e "${BLUE}📋 Step 5: Checking project file...${NC}"
if grep -q "NotificationServiceExtension" "bppulse.xcodeproj/project.pbxproj"; then
    echo -e "${GREEN}✅ NotificationServiceExtension target found in project${NC}"
else
    echo -e "${YELLOW}⚠️  NotificationServiceExtension target not found in project - you may need to add it manually in Xcode${NC}"
fi

echo ""
echo -e "${GREEN}🎉 Configuration check complete!${NC}"
echo ""
echo -e "${BLUE}📝 Next steps:${NC}"
echo "1. Run 'pod install' to install AirshipServiceExtension"
echo "2. Open bppulse.xcworkspace in Xcode"
echo "3. Verify the NotificationServiceExtension target is properly configured"
echo "4. Build and test with a rich push notification from Airship"
echo ""
echo -e "${YELLOW}💡 For testing, send a push notification with:${NC}"
echo "   - mutable-content: 1"
echo "   - com.urbanairship.image: <image_url>"
