<manifest xmlns:android="http://schemas.android.com/apk/res/android">
  <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION"/>
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION"/>
    <uses-permission android:name="com.google.android.gms.permission.AD_ID"/>
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS"/>
  <application
    android:name=".MainApplication"
    android:label="@string/app_name"
    android:icon="@mipmap/ic_launcher"
    android:roundIcon="@mipmap/ic_launcher"
    android:usesCleartextTraffic="true"
    android:allowBackup="false"
    android:theme="@style/BootTheme">
    <meta-data
      android:name="com.google.android.geo.API_KEY"
        android:value="@string/G_MAPS_ANDROID"/>
      <uses-library android:name="org.apache.http.legacy" android:required="false"/>
    <activity
      android:name=".MainActivity"
      android:label="@string/app_name"
      android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
      android:screenOrientation="portrait"
      android:launchMode="singleTask"
      android:taskAffinity=""
      android:windowSoftInputMode="adjustResize"
      android:exported="true">
      <intent-filter android:autoVerify="true">
        <action android:name="android.intent.action.MAIN" />
        <category android:name="android.intent.category.LAUNCHER" />
      </intent-filter>
      <!-- linking associations -->
      <intent-filter android:label="bp pulse" android:autoVerify="true">
        <action android:name="android.intent.action.VIEW" />
        <category android:name="android.intent.category.DEFAULT" />
        <category android:name="android.intent.category.BROWSABLE" />
        <data android:scheme="bppulse" android:host="bppulse" android:pathPrefix="/map" />
        <data android:scheme="bppulse" android:host="bppulse" android:pathPrefix="/uberpro_login" />
        <data android:scheme="bppulse" android:host="bppulse" android:pathPrefix="/credit_topup" />
        <data android:scheme="bppulse" android:host="bppulse" android:pathPrefix="/subscribe_now" />
        <data android:scheme="bppulse" android:host="bppulse" android:pathPrefix="/profile_transaction" />
        <data android:scheme="bppulse" android:host="bppulse" android:pathPrefix="/email_address_successfully_added" />
        <data android:scheme="bppulse" android:host="bppulse" android:pathPrefix="/offers" />
        <data android:scheme="sfdc"/>
        <data android:scheme="bppay" />
      </intent-filter>
      <intent-filter android:autoVerify="true">
        <action android:name="android.intent.action.VIEW" />
        <category android:name="android.intent.category.DEFAULT" />
        <category android:name="android.intent.category.BROWSABLE" />
        <data android:scheme="https" />
        <data android:host="redirect-stg-energyid.bpglobal.com"/>
        <data android:host="login-stg-energyid.bpglobal.com"/>
        <data android:path="/redirect/PulseontheGo" />
      </intent-filter>
      <intent-filter android:autoVerify="true">
        <action android:name="android.intent.action.VIEW" />
        <category android:name="android.intent.category.DEFAULT" />
        <category android:name="android.intent.category.BROWSABLE" />
        <data android:scheme="https" />
        <data android:host="redirect-energyid.bp.com"/>
        <data android:host="login-energyid.bp.com"/>
        <data android:path="/redirect/pulseotg-global-mobile" />
      </intent-filter>
      <intent-filter android:autoVerify="true">
        <action android:name="android.intent.action.VIEW" />
        <category android:name="android.intent.category.DEFAULT" />
        <category android:name="android.intent.category.BROWSABLE" />
        <data android:scheme="bppulse" />
        <data android:scheme="bppaywallet" />
      </intent-filter>
    </activity>
    <activity android:name="com.facebook.react.devsupport.DevSettingsActivity" />
  </application>
  <queries>
    <intent>
      <action android:name="android.intent.action.VIEW" />
      <data android:scheme="google.navigation" />
    </intent>
    <intent>
      <action android:name="android.intent.action.VIEW" />
      <data android:scheme="citymapper" />
    </intent>
    <intent>
      <action android:name="android.intent.action.VIEW" />
      <data android:scheme="waze" />
    </intent>
    <!-- Charge History Intent to view PDF invoices -->
    <intent>
      <action android:name="android.intent.action.VIEW" />
      <data android:mimeType="application/pdf" />
    </intent>
  </queries>
</manifest>
