import {
  HelpPageAnalyticsEvent,
  LoginAnalyticsEvent,
  MigrationFlowAnalyticsEvent,
  OutageAnalyticsEvent,
  SiteBannerAnalyticsEvent,
  TabsAnalyticsEvent,
  UberAnalyticsEvent,
  UberMigrationAnalyticsEvent,
} from '@analytics/enums';
import { WalletAnalyticsEvent } from '@bp/bppay-wallet-feature';
import { ChargeHistoryAnalyticsEvent } from '@bp/charge-history-mfe';
import { ChargeAnalyticsEvent } from '@bp/charge-mfe';
import { CreditAnalyticsEvent } from '@bp/credit-mfe';
import { FavouritesAnalyticsEvent } from '@bp/favourites-mfe';
import { GuestAnalyticsEvent } from '@bp/guest_feature-mfe';
import { MapAnalyticsEvent, Site } from '@bp/map-mfe';
import { SubsAnalyticsEvent } from '@bp/mfe-subscription';
import { OffersAnalyticsEvent } from '@bp/offers-mfe';
import { OnboardingAnalyticsEvent } from '@bp/onboarding-mfe';
import { PartnerDriverAnalyticsEvent } from '@bp/partnerdriver-mfe/dist/analytics';
import { ProfileAnalyticsEvent } from '@bp/profile-mfe';
import { ProfileAnalyticsEventType } from '@bp/profile-mfe/dist/analytics/events/types';
import { LoginAnalyticsEvent as AuthAnalyticsEventsType } from '@bp/pulse-auth-sdk';
import { RegAnalyticsEvent } from '@bp/registration-mfe';
import { RFIDAnalyticsEvent } from '@bp/rfid-mfe';
import { RTBFAnalyticsEvent } from '@bp/rtbf-mfe';
import { getUserInfo } from '@common/asyncStorage';
import analytics from '@react-native-firebase/analytics';
import { getLocationServicesStatus } from '@utils/locationStatus';
import { logger } from '@utils/logger';

import type { AnalyticsEventMap, AnalyticsEventType } from '../analytics.types';

/**
 * Formats the platform's logEvent function to include userInfo
 * with every event
 *
 */
const logCustomEvent = async (eventType: string, payload?: {}) => {
  try {
    const userInfo = await getUserInfo();
    await analytics().logEvent(eventType.trim(), { ...payload, ...userInfo });
  } catch (e) {
    logger.warn('There was an error in firebase.customAnalyticsEvent(): ', e);
  }
};

const logMapEvent = async (eventType: string, payload?: {}) => {
  try {
    const locationServicesStatus = await getLocationServicesStatus();

    logCustomEvent(eventType, {
      ...payload,
      isLocationServicesEnabled: locationServicesStatus,
    });
  } catch (e) {
    logger.warn('There was an error in firebase.customAnalyticsEvent(): ', e);
  }
};

const eventMap: AnalyticsEventMap = {
  [MapAnalyticsEvent.SEARCH_BAR_FOCUS]: () => {
    return logMapEvent('Map_SearchBox_Click');
  },
  [MapAnalyticsEvent.SEARCH_TEXT_CHANGE]: undefined,
  [MapAnalyticsEvent.SEARCH_LOCATION_SELECT]: () => {
    return logMapEvent('Map_SearchResult_SiteClick');
  },
  [MapAnalyticsEvent.SEARCH_HISTORY_LOCATION_SELECT]: () => {
    return logMapEvent('Map_SearchHistory_SiteClick');
  },
  [MapAnalyticsEvent.SITE_DETAILS_CLOSE]: undefined,
  [MapAnalyticsEvent.SITE_DETAILS_VIEW]: undefined,
  [MapAnalyticsEvent.SITE_DIRECTIONS_VIEW]: ({ site }) => {
    return logMapEvent('Map_Site_Directions_Click', {
      Site_ID: site.siteId,
      Site_Provider: site.provider,
      Site_Country: site.siteDetails?.country,
      CP_Scheme: site.chargepoints
        ?.flatMap((c: { schemes: Array<{ schemeName: string }> }) => c.schemes)
        .map((s: { schemeName: string }) => s.schemeName)
        .join(','),
      CP_Operator: site.cpo,
    });
  },
  [MapAnalyticsEvent.SITE_FAVOURITE_ADD]: undefined,
  [MapAnalyticsEvent.SITE_FAVOURITE_REMOVE]: undefined,
  [MapAnalyticsEvent.SITE_MARKER_SELECT]: ({ site }) => {
    return logMapEvent('Map_SiteMarker_Click', {
      Site_ID: site.siteId,
      Site_Provider: site.provider,
      Site_Country: site.country,
      CP_Scheme: site.evDetails?.schemes
        ?.map((s: { schemeName: string }) => s.schemeName)
        .join(','),
      CP_Operator: site.cpo,
    });
  },
  [MapAnalyticsEvent.NEARBY_SITES_OPEN]: () => {
    return logMapEvent('Map_NearbyLocations_Open');
  },
  [MapAnalyticsEvent.NEARBY_SITE_SELECT]: ({ site }) => {
    return logMapEvent('Map_NearbyLocations_SiteClick', {
      Site_ID: site.siteId,
      Site_Provider: site.provider,
      Site_Country: site.siteDetails?.country,
      CP_Scheme: site.chargepoints
        ?.flatMap((c: { schemes: Array<{ schemeName: string }> }) => c.schemes)
        .map((s: { schemeName: string }) => s.schemeName)
        .join(','),
    });
  },
  [MapAnalyticsEvent.QUICK_FILTER_CONNECTOR_TYPE_SELECT]: () => {
    return logMapEvent('Map_QuickFilters_ConnectorType_Click');
  },
  [MapAnalyticsEvent.QUICK_FILTER_CONNECTOR_TYPE_SAVE]: ({
    connectorTypes,
  }) => {
    return logMapEvent('Map_QuickFilters_ConnectorType_Save', {
      ConnectorTypes: connectorTypes.join(','),
    });
  },
  [MapAnalyticsEvent.QUICK_FILTER_SPEED_SELECT]: () => {
    return logMapEvent('Map_QuickFilters_Speed_Click');
  },
  [MapAnalyticsEvent.QUICK_FILTER_FAVOURITES_SELECT]: () => {
    return logMapEvent('Map_QuickFilters_Favourites_Click');
  },
  [MapAnalyticsEvent.LOGIN_TO_SHOW_FAVOURITES_SELECT]: () => {
    return logMapEvent('Map_LoginToShowFavourites_Login_Click');
  },
  [MapAnalyticsEvent.LOGIN_TO_SHOW_FAVOURITES_OPEN]: () => {
    return logMapEvent('Map_LoginToShowFavourites_Open');
  },
  [MapAnalyticsEvent.LOGIN_TO_SHOW_FAVOURITES_CLOSE]: () => {
    return logMapEvent('Map_LoginToShowFavourites_Close');
  },
  [MapAnalyticsEvent.CHARGEPOINT_CHARGE_START]: ({ chargepoint }) => {
    return logMapEvent('Map_CP_ChargeNow_Click', {
      Site_ID: chargepoint.site?.siteId,
      Site_Provider: chargepoint.provider,
      Site_Country: chargepoint.site?.siteDetails?.country,
      CP_Scheme: chargepoint.schemes
        ?.map((s: { schemeName: string }) => s.schemeName)
        .join(','),
      CP_Operator: chargepoint.site.cpo,
    });
  },
  [MapAnalyticsEvent.CHARGEPOINT_CHARGE_AS_GUEST_SELECT]: ({ chargepoint }) => {
    return logMapEvent('Map_CP_ChargeAsAGuest_Click', {
      Site_ID: chargepoint.site?.siteId,
      Site_Provider: chargepoint.provider,
      Site_Country: chargepoint.site?.siteDetails?.country,
      CP_Scheme: chargepoint.schemes
        ?.map((s: { schemeName: string }) => s.schemeName)
        .join(','),
    });
  },
  [MapAnalyticsEvent.FILTER_PAGE_OPEN]: () => {
    return logMapEvent('Map_FilterPage_Open');
  },
  [MapAnalyticsEvent.FILTER_PAGE_SAVE]: ({ filters }) => {
    return logMapEvent('Map_FilterPage_Save', filters);
  },
  [MapAnalyticsEvent.FILTER_PAGE_CLOSE]: () => {
    return logMapEvent('Map_FilterPage_Cancel');
  },
  [MapAnalyticsEvent.FILTER_PAGE_CLEAR]: () => {
    return logMapEvent('Map_FilterPage_Clear');
  },
  [MapAnalyticsEvent.MAP_RECENTER_SELECT]: () => {
    return logMapEvent('Map_Recentre_Click');
  },
  [MapAnalyticsEvent.MAP_SCREEN_OPEN]: () => {
    return logMapEvent('Map_MapOpen');
  },
  [MapAnalyticsEvent.LOCATION_SERVICE_DISABLED_OPEN]: () => {
    return logMapEvent('Map_Recentre_LocationDisabled_Open');
  },
  [MapAnalyticsEvent.LOCATION_SERVICE_DISABLED_CLOSE]: () => {
    return logMapEvent('Map_Recentre_LocationDisabled_Close');
  },
  [MapAnalyticsEvent.QUICK_FILTER_SHOW_PULSE_ONLY_SELECT]: () => {
    return logMapEvent('Map_QuickFilters_ShowPulseOnly_Click');
  },
  [MapAnalyticsEvent.QUICK_FILTER_SPEED_SAVE]: ({ speeds }) => {
    return logMapEvent('Map_QuickFilters_Speed_Save', {
      ChargerSpeeds: speeds.join(','),
    });
  },
  [MapAnalyticsEvent.OPERATOR_FILTERS_SAVE]: ({ operators }) => {
    return logMapEvent('Map_OperatorFilters_Save', {
      CP_Operators: operators.join(','),
    });
  },

  //Offer Analytics Events
  [OffersAnalyticsEvent.OFFERS_ADD_NEW_OFFER_CHECK_CLICK]: ({
    entered_value,
  }) => {
    return logCustomEvent('Offers_AddNewOffer_Check_Click', {
      Entered_Value: entered_value,
    });
  },
  [OffersAnalyticsEvent.OFFERS_ADD_NEW_OFFER_CHECK_INVALID]: ({
    entered_value,
    error_message,
  }) => {
    return logCustomEvent('Offers_AddNewOffer_Check_Invalid', {
      Entered_Value: entered_value,
      Error_Message: error_message,
    });
  },
  [OffersAnalyticsEvent.OFFERS_ADD_NEW_OFFER_CHECK_VALID]: ({
    contradicting_offer,
    offer_code,
  }) => {
    return logCustomEvent('Offers_AddNewOffer_Check_Valid', {
      Contradicting_Offer: contradicting_offer,
      Offer_Code: offer_code,
    });
  },
  [OffersAnalyticsEvent.OFFERS_ADD_NEW_OFFER_CONFIRM_CLICK]: ({
    offer_type,
    selected_new,
    offer_code,
  }) => {
    return logCustomEvent('Offers_AddNewOffer_Confirm_Click', {
      Selected_New: selected_new,
      Offer_Type: offer_type,
      Offer_Code: offer_code,
    });
  },
  [OffersAnalyticsEvent.OFFERS_ADD_NEW_OFFER_SCREEN_OPEN]: () => {
    return logCustomEvent('Offers_AddNewOfferScreen_Open');
  },
  [OffersAnalyticsEvent.OFFERS_COMING_SOON_OPEN]: ({ country, user_type }) => {
    return logCustomEvent('Offers_AddNewOffer_Confirm_Click', {
      Country: country,
      User_Type: user_type,
    });
  },
  [OffersAnalyticsEvent.OFFERS_ERROR_OFFER_NOT_ADDED_OPEN]: ({
    entered_value,
    error_message,
  }) => {
    return logCustomEvent('Offers_Error_OfferNotAdded_Open', {
      Entered_Value: entered_value,
      Error_Message: error_message,
    });
  },
  [OffersAnalyticsEvent.OFFERS_ERROR_OFFER_NOT_ADDED_GO_BACK_CLICK]: () => {
    return logCustomEvent('Offers_Error_OfferNotAdded_GoBack_Click');
  },
  [OffersAnalyticsEvent.OFFERS_ERROR_SCREEN_OPEN]: ({ error_message }) => {
    return logCustomEvent('Offers_ErrorScreen_Open', {
      Error_Message: error_message,
    });
  },
  [OffersAnalyticsEvent.OFFERS_LEGACY_PAYG_SUB_AND_SAVE_CLICK]: () => {
    return logCustomEvent('Offers_LegacyPAYG_SubAndSave_Click');
  },
  [OffersAnalyticsEvent.OFFERS_LEGACY_SUBS_MY_SUBSCRIPTION_CLICK]: () => {
    return logCustomEvent('Offers_LegacySubs_MySubscription_Click');
  },
  [OffersAnalyticsEvent.OFFERS_LOGIN_CLICK]: () => {
    return logCustomEvent('Offers_LogIn_Click');
  },
  [OffersAnalyticsEvent.OFFERS_LOGIN_CREATE_ACCOUNT_CLICK]: () => {
    return logCustomEvent('Offers_LogIn_CreateAccount_Click');
  },
  [OffersAnalyticsEvent.OFFERS_LOGIN_CREATE_ACCOUNT_OPEN]: () => {
    return logCustomEvent('Offers_CreateAccount_Open');
  },
  [OffersAnalyticsEvent.OFFERS_OFFER_ADDED_SUCCESS_OPEN]: ({
    offer_code,
    offer_type,
  }) => {
    return logCustomEvent('Offers_OfferAdded_Success_Open', {
      Entered_Value: offer_code,
      Offer_Type: offer_type,
    });
  },
  [OffersAnalyticsEvent.OFFERS_SCREEN_ACTIVE_OPEN]: undefined,
  [OffersAnalyticsEvent.OFFERS_SCREEN_NEW_OFFER_CLICK]: undefined,
  [OffersAnalyticsEvent.OFFERS_SCREEN_SUBSCRIBE_CLICK]: undefined,
  [OffersAnalyticsEvent.OFFERS_SCREEN_TCS_CLICK]: undefined,
  [OffersAnalyticsEvent.OFFERS_SCREEN_USED_EXPIRED_CLICK]: undefined,
  [OffersAnalyticsEvent.OFFERS_SCREEN_USED_EXPIRED_OPEN]: undefined,
  [OffersAnalyticsEvent.OFFERS_SUBS_REQUIRED_OPEN]: undefined,
  [OffersAnalyticsEvent.OFFERS_SUBS_REQUIRED_REMOVE_CLICK]: undefined,
  [OffersAnalyticsEvent.OFFERS_SUBS_REQUIRED_SETUP_CLICK]: undefined,

  // Partner Driver Analytics Events
  [PartnerDriverAnalyticsEvent.PARTNER_OFFERS_OPEN]: () => {
    return logCustomEvent('Partner_Offers_Open');
  },

  [PartnerDriverAnalyticsEvent.ADAC_ID_ENTRY_SCREEN_OPEN]: () => {
    return logCustomEvent('Partner_ADAC_IdEntryScreen_Open');
  },

  [PartnerDriverAnalyticsEvent.ADAC_ID_ENTRY_SCREEN_BACK]: ({ user_input }) => {
    return logCustomEvent('Partner_ADAC_IdEntryScreen_Back', {
      User_Input: user_input,
    });
  },

  [PartnerDriverAnalyticsEvent.ADAC_ID_ENTRY_SCREEN_CONTINUE]: ({
    user_input,
  }) => {
    return logCustomEvent('Partner_ADAC_IdEntryScreen_Continue', {
      User_Input: user_input,
    });
  },

  [PartnerDriverAnalyticsEvent.PARTNER_ADAC_LINK_RESULT_SCREEN_OPEN]: ({
    rfid_status,
    response_code,
    error_message,
  }) => {
    return logCustomEvent('Partner_ADAC_LinkResultScreen_Open', {
      RFID_Status: rfid_status,
      Response_Code: response_code,
      ErrorMessage: error_message,
    });
  },

  [PartnerDriverAnalyticsEvent.ADAC_LINK_RESULT_SCREEN_CTA_CLICK]: ({
    rfid_status,
    button_title,
  }) => {
    return logCustomEvent('Partner_ADAC_LinkResultScreen_CTAClick', {
      RFID_Status: rfid_status,
      ButtonTitle: button_title,
    });
  },

  [PartnerDriverAnalyticsEvent.ADAC_MEMBERSHIP_DETAILS_OPEN]: ({
    rfid_status,
  }) => {
    return logCustomEvent('Partner_ADAC_MembershipScreen_Open', {
      RFID_Status: rfid_status,
    });
  },

  [PartnerDriverAnalyticsEvent.ADAC_MEMBERSHIP_DETAILS_LEARN_MORE]: ({
    rfid_status,
  }) => {
    return logCustomEvent('Partner_ADAC_MembershipScreen_LearnMore', {
      RFID_Status: rfid_status,
    });
  },

  [PartnerDriverAnalyticsEvent.ADAC_MEMBERSHIP_DETAILS_GET_RFID]: ({
    rfid_status,
  }) => {
    return logCustomEvent('Partner_ADAC_MembershipScreen_GetRFID', {
      RFID_Status: rfid_status,
    });
  },

  [PartnerDriverAnalyticsEvent.ADAC_MEMBERSHIP_DETAILS_BACK]: ({
    rfid_status,
  }) => {
    return logCustomEvent('Partner_ADAC_MembershipScreen_Back', {
      RFID_Status: rfid_status,
    });
  },

  [PartnerDriverAnalyticsEvent.ADAC_LOADING_FAILED_SCREEN_OPEN]: ({
    error_message,
  }) => {
    return logCustomEvent('Partner_ADAC_LoadingFailedScreen_Open', {
      ErrorMessage: error_message,
    });
  },

  [PartnerDriverAnalyticsEvent.UBER_SETUP_ORDER_CHARGE_CARD_OPEN]: ({
    tag_notes,
  }) => {
    return logCustomEvent('UberSetup_OrderChargeCard_Open', {
      ...tag_notes,
    });
  },

  [PartnerDriverAnalyticsEvent.UBER_SETUP_LOGOUT_MODAL_LOGOUT_CLICK]: ({
    tag_notes,
    user_journey,
  }) => {
    return logCustomEvent('UberSetup_LogOutModal_LogOut_Click', {
      ...tag_notes,
      ...user_journey,
    });
  },

  [PartnerDriverAnalyticsEvent.UBER_SETUP_ORDER_CHARGE_CARD_ORDER_CLICK]: ({
    tag_notes,
  }) => {
    return logCustomEvent('UberSetup_OrderChargeCard_Order_Click', {
      ...tag_notes,
    });
  },

  [PartnerDriverAnalyticsEvent.UBER_SETUP_ORDER_CHARGE_CARD_LOGOUT_CLICK]: ({
    tag_notes,
    user_journey,
  }) => {
    return logCustomEvent('UberSetup_OrderChargeCard_LogOut_Click', {
      ...tag_notes,
      ...user_journey,
    });
  },

  [PartnerDriverAnalyticsEvent.PARTNER_UBER_OFFER_SCREEN_OPEN]: () => {
    return logCustomEvent('Partner_UberOfferScreen_Open', {});
  },

  [PartnerDriverAnalyticsEvent.PARTNER_UBER_OFFER_SCREEN_EXPLORE_CLICK]: () => {
    return logCustomEvent('Partner_UberOfferScreen_Explore_Click', {});
  },

  [PartnerDriverAnalyticsEvent.PARTNER_UBER_OFFER_SCREEN_UNLINK_CLICK]: () => {
    return logCustomEvent('Partner_UberOfferScreen_Unlink_Click', {});
  },

  [PartnerDriverAnalyticsEvent.UNLINK_ACCOUNTS_SUCCESS_SUBSCRIBE_CLICK]: ({
    user_journey,
  }) => {
    return logCustomEvent('UnlinkAccountsSuccess_Subscribe_Click', {
      user_journey,
    });
  },

  [PartnerDriverAnalyticsEvent.UNLINK_ACCOUNTS_SUCCESS_FIND_CHARGER_CLICK]: ({
    user_journey,
  }) => {
    return logCustomEvent('UnlinkAccountsSuccess_FindCharger_Click', {
      user_journey,
    });
  },

  [PartnerDriverAnalyticsEvent.ADAC_LOADING_FAILED_SCREEN_RETRY]: () => {
    return logCustomEvent('Partner_LoadingFailedScreen_Retry');
  },

  [PartnerDriverAnalyticsEvent.ADAC_LOADING_FAILED_SCREEN_BACK]: () => {
    return logCustomEvent('Partner_LoadingFailedScreen_Back');
  },

  [ProfileAnalyticsEvent.MARKETING_PREFERENCE_UPDATE_SAVE]: payload => {
    return logCustomEvent('Profile_MarketingPreference_Update_Save', payload);
  },
  [ProfileAnalyticsEvent.MARKETING_PREFERENCE_OPT_OUT_SAVE]: payload => {
    return logCustomEvent('Profile_MarketingPreference_OptOut_Save', payload);
  },
  [ProfileAnalyticsEvent.MARKETING_PREFERENCE_OPEN]: payload => {
    return logCustomEvent('Profile_MarketingPreference_Open', payload);
  },
  [ProfileAnalyticsEvent.PROFILE_SCREEN_LOGOUT_CLICK]: payload => {
    return logCustomEvent('Profile_ProfileScreen_Logout_Click', payload);
  },
  [ProfileAnalyticsEvent.PROFILE_SCREEN_LOGIN_CLICK]: payload => {
    return logCustomEvent('Profile_Login_Click', payload);
  },
  [ProfileAnalyticsEvent.PROFILE_SCREEN_REGISTER_CLICK]: payload => {
    return logCustomEvent('Profile_CreateAccount_Click', payload);
  },
  [ProfileAnalyticsEvent.PROFILE_LANGUAGE_SELECTOR_UPDATE]: payload => {
    return logCustomEvent('Profile_LanguageSelector_Update', payload);
  },
  [ProfileAnalyticsEvent.PROFILE_SCREEN_FEEDBACK_CLICK]: payload => {
    return logCustomEvent('Profile_ProfileScreen_Feedback_Click', payload);
  },
  [ProfileAnalyticsEvent.SETTINGS_SCREEN_TCS_CLICK]: payload => {
    return logCustomEvent('Profile_SettingsScreen_TCs_Click', payload);
  },
  [ProfileAnalyticsEvent.PROFILE_SCREEN_OPEN]: payload => {
    return logCustomEvent('Profile_ProfileScreen_Open', payload);
  },
  [ProfileAnalyticsEvent.ACCOUNT_DETAILS_SCREEN_OPEN]: payload => {
    return logCustomEvent('Profile_AccountDetailsScreen_Open', payload);
  },
  [ProfileAnalyticsEvent.SETTINGS_SCREEN_OPEN]: payload => {
    return logCustomEvent('Profile_SettingsScreen_Open', payload);
  },

  [ProfileAnalyticsEvent.PROFILE_PERSONAL_INFORMATION_EDIT_CLICK]: payload => {
    return logCustomEvent('Profile_PersonalInformation_EditClick', payload);
  },
  [ProfileAnalyticsEvent.PROFILE_PERSONAL_INFORMATION_DONE_CLICK]: payload => {
    return logCustomEvent('Profile_PersonalInformation_DoneClick', payload);
  },
  [ProfileAnalyticsEvent.PROFILE_PERSONAL_INFORMATION_ADD_ADDRESS_CLICK]:
    payload => {
      return logCustomEvent('Profile_PersonalInfo_AddAddressClick', payload);
    },
  [ProfileAnalyticsEvent.PROFILE_PERSONAL_INFORMATION_UPDATE_ADDRESS_CLICK]:
    payload => {
      return logCustomEvent('Profile_PersonalInfo_UpdateAddressClick', payload);
    },
  [ProfileAnalyticsEvent.PROFILE_ADD_ADDRESS_SCREEN_OPEN]: payload => {
    return logCustomEvent('Profile_AddAddresssScreen_Open', payload);
  },
  [ProfileAnalyticsEvent.PROFILE_ADD_ADDRESS_SCREEN_MANUAL_CLICK]: payload => {
    return logCustomEvent('Profile_AddAddresssScreen_ManualClick', payload);
  },
  [ProfileAnalyticsEvent.PROFILE_ADD_ADDRESS_SCREEN_SEARCH_ERROR]: payload => {
    return logCustomEvent('Profile_AddAddresssScreen_SearchError', payload);
  },
  [ProfileAnalyticsEvent.PROFILE_ADD_ADDRESS_SCREEN_BACK]: payload => {
    return logCustomEvent('Profile_AddAddressScreen_Back', payload);
  },
  [ProfileAnalyticsEvent.PROFILE_EXIT_MODAL_DELETE_CHANGES_CLICK]: payload => {
    return logCustomEvent('Profile_ExitModal_DeleteChangesClick', payload);
  },
  [ProfileAnalyticsEvent.PROFILE_EXIT_MODAL_KEEP_EDITING_CLICK]: payload => {
    return logCustomEvent('Profile_ExitModal_KeepEditingClick', payload);
  },
  [ProfileAnalyticsEvent.PROFILE_ADD_ADDRESS_SCREEN_SAVE_CLICK]: payload => {
    return logCustomEvent('Profile_AddAddressScreen_SaveClick', payload);
  },
  [ProfileAnalyticsEvent.PROFILE_UPDATE_DETAILS_SUCCESSFUL_OPEN]: payload => {
    return logCustomEvent('Profile_UpdateDetails_SuccessfulOpen', payload);
  },
  [ProfileAnalyticsEvent.PROFILE_UPDATE_DETAILS_SUCCESSFUL_FINISH]: payload => {
    return logCustomEvent('Profile_UpdateDetailsSuccessful_Finish', payload);
  },

  [ProfileAnalyticsEvent.PROFILE_UPDATE_DETAILS_FAILED_OPEN]: payload => {
    return logCustomEvent('Profile_UpdateDetails_FailedOpen', payload);
  },
  [ProfileAnalyticsEvent.PROFILE_UPDATE_DEATILS_FAIL_TRY_AGAIN_CLICK]:
    payload => {
      return logCustomEvent('Profile_UpdateDetailsFail_TryAgainClick', payload);
    },
  [ProfileAnalyticsEvent.PROFILE_UPDATE_DETAILS_FAIL_EXIT_CLICK]: payload => {
    return logCustomEvent('Profile_UpdateDetailsFail_ExitClick', payload);
  },
  [ProfileAnalyticsEvent.PROFILE_UPDATE_NAME_SCREEN_OPEN]: payload => {
    return logCustomEvent('Profile_UpdateNameScreen_Open', payload);
  },
  [ProfileAnalyticsEvent.PROFILE_UPDATE_NAME_SCREEN_BACK]: payload => {
    return logCustomEvent('Profile_UpdateNameScreen_Back', payload);
  },
  [ProfileAnalyticsEvent.PROFILE_UPDATE_NAME_SUCCESS_OPEN]: payload => {
    return logCustomEvent('Profile_UpdateNameSuccess_Open', payload);
  },
  [ProfileAnalyticsEvent.PROFILE_UPDATE_NAME_SUCCESS_FINISH]: payload => {
    return logCustomEvent('Profile_UpdateNameSuccess_Finish', payload);
  },
  [ProfileAnalyticsEvent.PROFILE_UPDATE_NAME_FAILURE_OPEN]: payload => {
    return logCustomEvent('Profile_UpdateNameFailure_Open', payload);
  },
  [ProfileAnalyticsEvent.PROFILE_UPDATE_NAME_FAILURE_FINISH]: payload => {
    return logCustomEvent('Profile_UpdateNameFailure_Finish', payload);
  },

  [ProfileAnalyticsEvent.PROFILE_UPDATE_EMAIL_SCREEN_OPEN]: payload => {
    return logCustomEvent('Profile_UpdateEmailScreen_Open', payload);
  },
  [ProfileAnalyticsEvent.PROFILE_UPDATE_EMAIL_SCREEN_BACK]: payload => {
    return logCustomEvent('Profile_UpdateEmailScreen_Back', payload);
  },
  [ProfileAnalyticsEvent.PROFILE_UPDATE_EMAIL_VERIFY_OPEN]: payload => {
    return logCustomEvent('Profile_UpdateEmailVerify_Open', payload);
  },
  [ProfileAnalyticsEvent.PROFILE_UPDATE_EMAIL_SUCCESS_OPEN]: payload => {
    return logCustomEvent('Profile_UpdateEmailSuccess_Open', payload);
  },
  [ProfileAnalyticsEvent.PROFILE_UPDATE_EMAIL_SUCCESS_FINISH]: payload => {
    return logCustomEvent('Profile_UpdateEmailSuccess_Finish', payload);
  },
  [ProfileAnalyticsEvent.PROFILE_UPDATE_EMAIL_FAILURE_OPEN]: payload => {
    return logCustomEvent('Profile_UpdateEmailFailure_Open', payload);
  },
  [ProfileAnalyticsEvent.PROFILE_UPDATE_EMAIL_FAILURE_FINISH]: payload => {
    return logCustomEvent('Profile_UpdateEmailFailure_Finish', payload);
  },

  [ProfileAnalyticsEvent.PROFILE_UPDATE_MOBILE_SCREEN_OPEN]: payload => {
    return logCustomEvent('Profile_UpdateMobileScreen_Open', payload);
  },
  [ProfileAnalyticsEvent.PROFILE_UPDATE_MOBILE_SCREEN_BACK]: payload => {
    return logCustomEvent('Profile_UpdateMobileScreen_Back', payload);
  },
  [ProfileAnalyticsEvent.PROFILE_UPDATE_MOBILE_VERIFY_OPEN]: payload => {
    return logCustomEvent('Profile_UpdateMobileVerify_Open', payload);
  },
  [ProfileAnalyticsEvent.PROFILE_UPDATE_MOBILE_VERIFY_FINISH]: payload => {
    return logCustomEvent('Profile_UpdateMobileVerify_Finish', payload);
  },
  [ProfileAnalyticsEvent.PROFILE_UPDATE_MOBILE_SUCCESS_OPEN]: payload => {
    return logCustomEvent('Profile_UpdateMobileSuccess_Open', payload);
  },
  [ProfileAnalyticsEvent.PROFILE_UPDATE_MOBILE_SUCCESS_FINISH]: payload => {
    return logCustomEvent('Profile_UpdateMobileSuccess_Finish', payload);
  },
  [ProfileAnalyticsEvent.PROFILE_UPDATE_MOBILE_FAILURE_OPEN]: payload => {
    return logCustomEvent('Profile_UpdateMobileFailure_Open', payload);
  },
  [ProfileAnalyticsEvent.PROFILE_UPDATE_MOBILE_FAILURE_FINISH]: payload => {
    return logCustomEvent('Profile_UpdateMobileFailure_Finish', payload);
  },
  [HelpPageAnalyticsEvent.HELP_SCREEN_FAQS_CLICK]: () => {
    return logCustomEvent('Help_HelpScreen_FAQs_Click');
  },
  [HelpPageAnalyticsEvent.HELP_SCREEN_PHONE_NUMBER_CLICK]: () => {
    return logCustomEvent('Help_HelpScreen_PhoneNumber_Click');
  },
  [HelpPageAnalyticsEvent.HELP_SCREEN_EMAIL_CLICK]: () => {
    return logCustomEvent('Help_HelpScreen_Email_Click');
  },
  [HelpPageAnalyticsEvent.HELP_WEB_FORM_CLICK]: payload => {
    return logCustomEvent('Help_WebForm_Click', payload);
  },
  [HelpPageAnalyticsEvent.HELP_PARTNER_SITE_CLICK]: payload => {
    return logCustomEvent('Help_PartnerSite_Click', payload);
  },

  [AuthAnalyticsEventsType.LOGIN_ERROR]: payload => {
    return logCustomEvent('CIP_Login_Failure', payload);
  },
  [AuthAnalyticsEventsType.LOGIN_SCREEN_CLOSE]: payload => {
    return logCustomEvent('Login_LoginScreen_Close', payload);
  },
  [AuthAnalyticsEventsType.CIP_REGISTRATION_ERROR]: ({ errorMessage }) =>
    logCustomEvent('CIP_Registration_Error', { errorMessage }),
  [AuthAnalyticsEventsType.CIP_REGISTRATION_SUCCESS]: () =>
    logCustomEvent('CIP_Registration_Success'),
  [AuthAnalyticsEventsType.CIP_LOGIN_SUCCESS]: () =>
    logCustomEvent('CIP_Login_Success'),

  [LoginAnalyticsEvent.LOGIN_CLICK]: payload => {
    return logCustomEvent('LogIn_Click', payload);
  },
  [LoginAnalyticsEvent.CREATE_ACCOUNT_CLICK]: payload => {
    return logCustomEvent('CreateAccount_Click', payload);
  },
  [LoginAnalyticsEvent.FIND_CHARGER_MAP_BANNER]: payload => {
    return logCustomEvent('FindCharger_Click', payload);
  },
  [LoginAnalyticsEvent.CONFIRM_UBER_DRIVER_CLICK]: payload => {
    return logCustomEvent('ConfirmUberDriver_Click', payload);
  },

  [TabsAnalyticsEvent.CHARGE_TAB_ACTIVE_CHARGE_CLICK]: payload => {
    return logCustomEvent('ChargeTab_ActiveCharge_Click', payload);
  },
  [TabsAnalyticsEvent.CHARGE_TAB_NO_CHARGE_CLICK]: payload => {
    return logCustomEvent('ChargeTab_NoCharge_Click', payload);
  },
  [TabsAnalyticsEvent.MAP_TAB_CLICK]: payload => {
    return logCustomEvent('MapTab_Click', payload);
  },
  [TabsAnalyticsEvent.PROFILE_TAB_CLICK]: payload => {
    return logCustomEvent('ProfileTab_Click', payload);
  },
  [TabsAnalyticsEvent.HELP_TAB_CLICK]: payload => {
    return logCustomEvent('HelpTab_Click', payload);
  },

  [OutageAnalyticsEvent.OUTAGE_FULL_OUTAGE_SCREEN_OPEN]: payload => {
    return logCustomEvent('Outage_FullOutageScreen_Open', payload);
  },
  [OutageAnalyticsEvent.OUTAGE_PARTIAL_OUTAGE_BOTTOM_SHEET_OPEN]: payload => {
    return logCustomEvent('Outage_PartialOutageBottomSheet_Open', payload);
  },
  [OutageAnalyticsEvent.OUTAGE_PARTIAL_OUTAGE_BOTTOM_SHEET_CLOSE]: payload => {
    return logCustomEvent('Outage_PartialOutageBottomSheet_Close', payload);
  },

  // Charge History Analytics
  [ChargeHistoryAnalyticsEvent.HISTORY_ACTIVITY_OPEN]: payload => {
    return logCustomEvent('History_Activity_Open', payload);
  },
  [ChargeHistoryAnalyticsEvent.HISTORY_ACTIVITY_CLICK]: payload => {
    return logCustomEvent('History_Activity_Click', payload);
  },
  [ChargeHistoryAnalyticsEvent.HISTORY_ACTIVITYVIEW_DOWNLOADVAT_CLICK]:
    payload => {
      return logCustomEvent('History_ActivityView_DownloadVAT_Click', payload);
    },
  [ChargeHistoryAnalyticsEvent.HISTORY_ACTIVITYVIEW_DOWNLOADVAT_FAILED]:
    payload => {
      return logCustomEvent('History_ActivityView_DownloadVAT_Failed', payload);
    },
  [ChargeHistoryAnalyticsEvent.HISTORY_ACTIVITYVIEW_ADDADDRESS]: payload => {
    return logCustomEvent('History_ActivityView_AddAddress', payload);
  },
  [ChargeHistoryAnalyticsEvent.HISTORY_ACTIVITY_UNPAIDVIEW_MAKEPAYMENT]:
    payload => {
      return logCustomEvent('History_Activity_UnpaidView_MakePayment', payload);
    },
  [ChargeHistoryAnalyticsEvent.HISTORY_ACTIVITYVIEW_DOWNLOADCREDITNOTE]:
    payload => {
      return logCustomEvent('History_ActivityView_DownloadCreditNote', payload);
    },
  [ChargeHistoryAnalyticsEvent.HISTORY_ACTIVITYVIEW_DOWNLOADORIGINAL]:
    payload => {
      return logCustomEvent('History_ActivityView_DownloadOriginal', payload);
    },
  [ChargeHistoryAnalyticsEvent.HISTORY_ACTIVITYVIEW_CREDITNOTE_FAILED]:
    payload => {
      return logCustomEvent('History_ActivityView_CreditNote_Failed', payload);
    },
  [ChargeHistoryAnalyticsEvent.HISTORY_ACTIVITYVIEW_REFUND_ADDADDRESS]:
    payload => {
      return logCustomEvent('History_ActivityView_Refund_AddAddress', payload);
    },
  [ChargeHistoryAnalyticsEvent.HISTORY_SUBSINVOICE_CLICK]: payload => {
    return logCustomEvent('History_SUBSInvoice_Click', payload);
  },
  [ChargeHistoryAnalyticsEvent.HISTORY_SUBSINVOICE_CHARGINGFEES_CLICK]:
    payload => {
      return logCustomEvent('History_SUBSInvoice_ChargingFees_Click', payload);
    },
  [ChargeHistoryAnalyticsEvent.HISTORY_SUBSINVOICE_DOWNLOADVAT_FAILED]:
    payload => {
      return logCustomEvent('History_SubsInvoice_DownloadVAT_Failed', payload);
    },
  [ChargeHistoryAnalyticsEvent.HISTORY_SUBSINVOICE_DOWNLOADVAT_CLICK]:
    payload => {
      return logCustomEvent('History_SubsInvoice_DownloadVAT_Click', payload);
    },
  [ChargeHistoryAnalyticsEvent.HISTORY_SUBSCRIPTIONINVOICESTAB_CLICK]:
    payload => {
      return logCustomEvent('History_SubscriptionInvoicesTab_Click', payload);
    },

  [ChargeAnalyticsEvent.CHARGE_START_SCREEN_OPEN]: ({
    chargepoint,
    connector,
  }) => {
    return logCustomEvent('Charge_StartScreen_Open', {
      CP_ID: chargepoint?.apolloInternalId,
      Site_Provider: chargepoint?.provider,
      Site_Country: chargepoint?.site?.siteDetails?.country,
      ConnectorType: connector?.type,
      CP_Scheme: chargepoint?.schemes[0]?.schemeName,
    });
  },
  [ChargeAnalyticsEvent.CHARGE_START_SCREEN_CLOSE]: ({ chargepoint }) => {
    return logCustomEvent('Charge_StartScreen_Close', {
      CP_ID: chargepoint?.apolloInternalId,
      Site_Provider: chargepoint?.provider,
      Site_Country: chargepoint?.site?.siteDetails?.country,
      CP_Scheme: chargepoint?.schemes[0]?.schemeName,
    });
  },
  [ChargeAnalyticsEvent.CHARGE_START_SCREEN_CONNECTOR_CHANGE]: ({
    chargepoint,
  }) => {
    return logCustomEvent('Charge_StartScreen_Connector_Change', {
      CP_ID: chargepoint?.apolloInternalId,
      Site_Provider: chargepoint?.provider,
      Site_Country: chargepoint?.site?.siteDetails?.country,
      CP_Scheme: chargepoint?.schemes[0]?.schemeName,
    });
  },
  [ChargeAnalyticsEvent.CHARGE_START_SCREEN_CONNECTOR_CONFIRM]: ({
    chargepoint,
    connector,
  }) => {
    return logCustomEvent('Charge_StartScreen_Connector_Confirm', {
      CP_ID: chargepoint?.apolloInternalId,
      Site_Provider: chargepoint?.provider,
      Site_Country: chargepoint?.site?.siteDetails?.country,
      ConnectorType: connector?.type,
      CP_Scheme: chargepoint?.schemes[0]?.schemeName,
    });
  },
  [ChargeAnalyticsEvent.CHARGE_START_SCREEN_START_CHARGE_CLICK]: ({
    chargepoint,
    connector,
  }) => {
    return logCustomEvent('Charge_StartScreen_StartCharge_Click', {
      CP_ID: chargepoint?.apolloInternalId,
      Site_Provider: chargepoint?.provider,
      Site_Country: chargepoint?.site?.siteDetails?.country,
      ConnectorType: connector?.type,
      CP_Scheme: chargepoint?.schemes[0]?.schemeName,
    });
  },
  [ChargeAnalyticsEvent.CHARGE_CONNECTING_SCREEN_OPEN]: ({
    chargepoint,
    connector,
    user_journey,
  }) => {
    return logCustomEvent('Charge_ChargeConnectingScreen_Open', {
      CP_ID: chargepoint?.apolloInternalId,
      Site_Provider: chargepoint?.provider,
      Site_Country: chargepoint?.site?.siteDetails?.country,
      ConnectorType: connector?.type,
      Connector_ID: connector?.connectorInternalId,
      CP_Scheme: chargepoint?.schemes[0]?.schemeName,
      CP_Operator: chargepoint?.site?.cpo,
      user_journey,
    });
  },
  [ChargeAnalyticsEvent.CHARGE_START_CHARGE_REQUEST_SUCCESSFUL]: ({
    chargepoint,
    connector,
    user_journey,
  }) => {
    return logCustomEvent('Charge_StartChargeRequest_Successful', {
      CP_ID: chargepoint?.apolloInternalId,
      Site_Provider: chargepoint?.provider,
      Site_Country: chargepoint?.site?.siteDetails?.country,
      ConnectorType: connector?.type,
      Connector_ID: connector?.connectorInternalId,
      CP_Scheme: chargepoint?.schemes[0]?.schemeName,
      CP_Operator: chargepoint?.site?.cpo,
      user_journey,
    });
  },
  [ChargeAnalyticsEvent.CHARGE_START_CHARGE_REQUEST_UNSUCCESSFUL]: ({
    chargepoint,
    connector,
    user_journey,
  }) => {
    return logCustomEvent('Charge_StartChargeRequest_Unsuccessful', {
      CP_ID: chargepoint?.apolloInternalId,
      Site_Provider: chargepoint?.provider,
      Site_Country: chargepoint?.site?.siteDetails?.country,
      ConnectorType: connector?.type,
      Connector_ID: connector?.connectorInternalId,
      CP_Scheme: chargepoint?.schemes[0]?.schemeName,
      CP_Operator: chargepoint?.site?.cpo,
      user_journey,
    });
  },
  [ChargeAnalyticsEvent.CHARGE_START_CHARGE_ACCEPTED_SUCCESSFUL]: ({
    chargepoint,
    connector,
    user_journey,
  }) => {
    return logCustomEvent('Charge_StartChargeAccepted_Successful', {
      CP_ID: chargepoint?.apolloInternalId,
      Site_Provider: chargepoint?.provider,
      Site_Country: chargepoint?.site?.siteDetails?.country,
      ConnectorType: connector?.type,
      Connector_ID: connector?.connectorInternalId,
      CP_Scheme: chargepoint?.schemes[0]?.schemeName,
      CP_Operator: chargepoint?.site?.cpo,
      user_journey,
    });
  },
  [ChargeAnalyticsEvent.CHARGE_START_CHARGE_ACCEPTED_UNSUCCESSFUL]: ({
    chargepoint,
    connector,
    ErrorMessage,
    user_journey,
  }) => {
    return logCustomEvent('Charge_StartChargeAccepted_Unsuccessful', {
      CP_ID: chargepoint?.apolloInternalId,
      Site_Provider: chargepoint?.provider,
      Site_Country: chargepoint?.site?.siteDetails?.country,
      ConnectorType: connector?.type,
      Connector_ID: connector?.connectorInternalId,
      ErrorMessage,
      CP_Scheme: chargepoint?.schemes[0]?.schemeName,
      CP_Operator: chargepoint?.site?.cpo,
      user_journey,
    });
  },

  [ChargeAnalyticsEvent.CHARGE_START_CHARGE_ERROR_DISCONNECT]: ({
    chargepoint,
    connector,
    ErrorMessage,
    user_journey,
  }) => {
    return logCustomEvent('Charge_StartChargeError_Disconnect', {
      CP_ID: chargepoint?.apolloInternalId,
      Site_Provider: chargepoint?.provider,
      Site_Country: chargepoint?.site?.siteDetails?.country,
      ConnectorType: connector?.type,
      ErrorMessage,
      CP_Scheme: chargepoint?.schemes[0]?.schemeName,
      CP_Operator: chargepoint?.site?.cpo,
      Connector_ID: connector?.connectorInternalId,
      user_journey,
    });
  },
  [ChargeAnalyticsEvent.CHARGE_START_CHARGE_ERROR_CALL_CLICK]: ({
    chargepoint,
    connector,
    ErrorMessage,
    user_journey,
  }) => {
    return logCustomEvent('Charge_StartChargeError_CallClick', {
      CP_ID: chargepoint?.apolloInternalId,
      Site_Provider: chargepoint?.provider,
      Site_Country: chargepoint?.site?.siteDetails?.country,
      ConnectorType: connector?.type,
      ErrorMessage,
      CP_Scheme: chargepoint?.schemes[0]?.schemeName,
      CP_Operator: chargepoint?.site?.cpo,
      Connector_ID: connector?.connectorInternalId,
      user_journey,
    });
  },
  [ChargeAnalyticsEvent.CHARGE_MONITORING_NO_DATA_RETRY_CLICK]: ({
    chargepoint,
    connector,
    user_journey,
  }) => {
    return logCustomEvent('Charge_Monitoring_NoData_RetryClick', {
      CP_ID: chargepoint?.apolloInternalId,
      Site_Provider: chargepoint?.provider,
      Site_Country: chargepoint?.site?.siteDetails?.country,
      ConnectorType: connector?.type,
      CP_Scheme: chargepoint?.schemes[0]?.schemeName,
      CP_Operator: chargepoint?.site?.cpo,
      Connector_ID: connector?.connectorInternalId,
      user_journey,
    });
  },
  [ChargeAnalyticsEvent.CHARGE_MONITORING_NO_DATA_END_CLICK]: ({
    chargepoint,
    connector,
    user_journey,
  }) => {
    return logCustomEvent('Charge_Monitoring_NoData_EndClick', {
      CP_ID: chargepoint?.apolloInternalId,
      Site_Provider: chargepoint?.provider,
      Site_Country: chargepoint?.site?.siteDetails?.country,
      ConnectorType: connector?.type,
      CP_Scheme: chargepoint?.schemes[0]?.schemeName,
      CP_Operator: chargepoint?.site?.cpo,
      Connector_ID: connector?.connectorInternalId,
      user_journey,
    });
  },
  [ChargeAnalyticsEvent.CHARGE_START_CHARGE_SUCCESSFUL]: ({
    chargepoint,
    connector,
    user_journey,
  }) => {
    return logCustomEvent('Charge_StartChargeSuccessful', {
      CP_ID: chargepoint?.apolloInternalId,
      Site_Provider: chargepoint?.provider,
      Site_Country: chargepoint?.site?.siteDetails?.country,
      ConnectorType: connector?.type,
      Connector_ID: connector?.connectorInternalId,
      CP_Scheme: chargepoint?.schemes[0]?.schemeName,
      CP_Operator: chargepoint?.site?.cpo,
      user_journey,
    });
  },
  [ChargeAnalyticsEvent.CHARGE_START_CHARGE_RESPONSE_UNSUCCESSFUL]: ({
    chargepoint,
    connector,
    ErrorMessage,
    user_journey,
  }) => {
    return logCustomEvent('Charge_StartChargeResponse_Unsuccessful', {
      CP_ID: chargepoint?.apolloInternalId,
      Site_Provider: chargepoint?.provider,
      Site_Country: chargepoint?.site?.siteDetails?.country,
      ConnectorType: connector?.type,
      Connector_ID: connector?.connectorInternalId,
      ErrorMessage,
      CP_Scheme: chargepoint?.schemes[0]?.schemeName,
      CP_Operator: chargepoint?.site?.cpo,
      user_journey,
    });
  },
  [ChargeAnalyticsEvent.CHARGE_START_CHARGE_UNSUCCESSFUL]: ({
    chargepoint,
    connector,
    ErrorMessage,
    user_journey,
  }) => {
    return logCustomEvent('Charge_StartChargeUnsuccessful', {
      CP_ID: chargepoint?.apolloInternalId,
      Site_Provider: chargepoint?.provider,
      Site_Country: chargepoint?.site?.siteDetails?.country,
      ConnectorType: connector?.type,
      Connector_ID: connector?.connectorInternalId,
      ErrorMessage,
      CP_Scheme: chargepoint?.schemes[0]?.schemeName,
      CP_Operator: chargepoint?.site?.cpo,
      user_journey,
    });
  },
  [ChargeAnalyticsEvent.CHARGE_START_CHARGE_ERROR_TRY_AGAIN]: ({
    chargepoint,
    connector,
    ErrorMessage,
    user_journey,
  }) => {
    return logCustomEvent('Charge_StartChargeError_TryAgain', {
      CP_ID: chargepoint?.apolloInternalId,
      Site_Provider: chargepoint?.provider,
      Site_Country: chargepoint?.site?.siteDetails?.country,
      ConnectorType: connector?.type,
      Connector_ID: connector?.connectorInternalId,
      ErrorMessage,
      CP_Scheme: chargepoint?.schemes[0]?.schemeName,
      CP_Operator: chargepoint?.site?.cpo,
      user_journey,
    });
  },
  [ChargeAnalyticsEvent.CHARGE_START_CHARGE_ERROR_GO_BACK]: ({
    chargepoint,
    connector,
    ErrorMessage,
    user_journey,
  }) => {
    return logCustomEvent('Charge_StartChargeError_GoBack', {
      CP_ID: chargepoint?.apolloInternalId,
      Site_Provider: chargepoint?.provider,
      Site_Country: chargepoint?.site?.siteDetails?.country,
      ConnectorType: connector?.type,
      Connector_ID: connector?.connectorInternalId,
      ErrorMessage,
      CP_Scheme: chargepoint?.schemes[0]?.schemeName,
      CP_Operator: chargepoint?.site?.cpo,
      user_journey,
    });
  },
  [ChargeAnalyticsEvent.CHARGE_CONNECTING_RENEWABLE_EXPANDER_OPEN]: ({
    chargepoint,
    connector,
  }) => {
    return logCustomEvent('Charge_Connecting_RenewableExpander_Open', {
      CP_ID: chargepoint?.apolloInternalId,
      Site_Provider: chargepoint?.provider,
      Site_Country: chargepoint?.site?.siteDetails?.country,
      ConnectorType: connector?.type,
      Connector_ID: connector?.connectorInternalId,
      CP_Scheme: chargepoint?.schemes[0]?.schemeName,
    });
  },
  [ChargeAnalyticsEvent.CHARGE_START_CHARGE_LONG_WAIT]: ({
    chargepoint,
    connector,
    isChargeReqAccepted,
    user_journey,
  }) => {
    return logCustomEvent('Charge_LongWait', {
      CP_ID: chargepoint?.apolloInternalId,
      Site_Provider: chargepoint?.provider,
      Site_Country: chargepoint?.site?.siteDetails?.country,
      ConnectorType: connector?.type,
      Connector_ID: connector?.connectorInternalId,
      CP_Operator: chargepoint?.site?.cpo,
      request_response: isChargeReqAccepted
        ? 'Start_Charge_Response'
        : 'Start_Charge_Accepted',
      user_journey,
    });
  },
  [ChargeAnalyticsEvent.CHARGE_START_CHARGE_LONG_WAIT_WAIT]: ({
    chargepoint,
    connector,
    isChargeReqAccepted,
    user_journey,
  }) => {
    return logCustomEvent('Charge_LongWait_WaitClick', {
      CP_ID: chargepoint?.apolloInternalId,
      Site_Provider: chargepoint?.provider,
      Site_Country: chargepoint?.site?.siteDetails?.country,
      ConnectorType: connector?.type,
      Connector_ID: connector?.connectorInternalId,
      CP_Operator: chargepoint?.site?.cpo,
      request_response: isChargeReqAccepted
        ? 'Start_Charge_Response'
        : 'Start_Charge_Accepted',
      user_journey,
    });
  },
  [ChargeAnalyticsEvent.CHARGE_START_CHARGE_LONG_WAIT_DISC]: ({
    chargepoint,
    connector,
    isChargeReqAccepted,
    user_journey,
  }) => {
    return logCustomEvent('Charge_LongWait_DisconnectClick', {
      CP_ID: chargepoint?.apolloInternalId,
      Site_Provider: chargepoint?.provider,
      Site_Country: chargepoint?.site?.siteDetails?.country,
      ConnectorType: connector?.type,
      Connector_ID: connector?.connectorInternalId,
      CP_Operator: chargepoint?.site?.cpo,
      request_response: isChargeReqAccepted
        ? 'Start_Charge_Response'
        : 'Start_Charge_Accepted',
      user_journey,
    });
  },
  [ChargeAnalyticsEvent.CHARGE_START_CHARGE_APP_LONG_WAIT_CALL]: ({
    chargepoint,
    connector,
    isChargeReqAccepted,
    user_journey,
  }) => {
    return logCustomEvent('Charge_LongWait_CallClick', {
      CP_ID: chargepoint?.apolloInternalId,
      Site_Provider: chargepoint?.provider,
      Site_Country: chargepoint?.site?.siteDetails?.country,
      ConnectorType: connector?.type,
      Connector_ID: connector?.connectorInternalId,
      CP_Operator: chargepoint?.site?.cpo,
      request_response: isChargeReqAccepted
        ? 'Start_Charge_Response'
        : 'Start_Charge_Accepted',
      user_journey,
    });
  },
  [ChargeAnalyticsEvent.CHARGE_START_CHARGE_APP_TIMEOUT]: ({
    chargepoint,
    connector,
    isChargeReqAccepted,
    user_journey,
  }) => {
    return logCustomEvent('Charge_AppTimeOut', {
      CP_ID: chargepoint?.apolloInternalId,
      Site_Provider: chargepoint?.provider,
      Site_Country: chargepoint?.site?.siteDetails?.country,
      ConnectorType: connector?.type,
      Connector_ID: connector?.connectorInternalId,
      CP_Operator: chargepoint?.site?.cpo,
      request_response: isChargeReqAccepted
        ? 'Start_Charge_Response'
        : 'Start_Charge_Accepted',
      user_journey,
    });
  },
  [ChargeAnalyticsEvent.CHARGE_START_CHARGE_APP_TIMEOUT_DISC]: ({
    chargepoint,
    connector,
    isChargeReqAccepted,
    user_journey,
  }) => {
    return logCustomEvent('Charge_AppTimeOut_DisconnectClick', {
      CP_ID: chargepoint?.apolloInternalId,
      Site_Provider: chargepoint?.provider,
      Site_Country: chargepoint?.site?.siteDetails?.country,
      ConnectorType: connector?.type,
      Connector_ID: connector?.connectorInternalId,
      CP_Operator: chargepoint?.site?.cpo,
      request_response: isChargeReqAccepted
        ? 'Start_Charge_Response'
        : 'Start_Charge_Accepted',
      user_journey,
    });
  },
  [ChargeAnalyticsEvent.CHARGE_START_CHARGE_APP_TIMEOUT_CALL]: ({
    chargepoint,
    connector,
    isChargeReqAccepted,
    user_journey,
  }) => {
    return logCustomEvent('Charge_AppTimeOut_CallClick', {
      CP_ID: chargepoint?.apolloInternalId,
      Site_Provider: chargepoint?.provider,
      Site_Country: chargepoint?.site?.siteDetails?.country,
      ConnectorType: connector?.type,
      Connector_ID: connector?.connectorInternalId,
      CP_Operator: chargepoint?.site?.cpo,
      request_response: isChargeReqAccepted
        ? 'Start_Charge_Response'
        : 'Start_Charge_Accepted',
      user_journey,
    });
  },
  [ChargeAnalyticsEvent.NO_ACTIVE_SESSION_SCREEN_OPEN]: () => {
    return logCustomEvent('Charge_NoActiveSessionScreen_Open');
  },
  [ChargeAnalyticsEvent.NO_ACTIVE_SESSION_SCREEN_FIND_A_CHARGER_SELECT]: () => {
    return logCustomEvent('Charge_NoChargeScreen_FindACharger_Click');
  },
  [ChargeAnalyticsEvent.CHARGE_MONITORING_SCREEN_OPEN]: ({
    chargepoint,
    connector,
    user_journey,
  }) => {
    return logCustomEvent('Charge_MonitoringScreen_Open', {
      CP_ID: chargepoint?.apolloInternalId,
      Site_Provider: chargepoint?.provider,
      Site_Country: chargepoint?.site?.siteDetails?.country,
      ConnectorType: connector?.type,
      Connector_ID: connector?.connectorInternalId,
      CP_Scheme: chargepoint?.schemes[0]?.schemeName,
      CP_Operator: chargepoint?.site?.cpo,
      user_journey,
    });
  },
  [ChargeAnalyticsEvent.CHARGE_MONITORING_STOP_CHARGE_STEPS_CLICK]: ({
    chargepoint,
    connector,
    user_journey,
  }) => {
    return logCustomEvent('Charge_Monitoring_StopChargeSteps_Click', {
      CP_ID: chargepoint?.apolloInternalId,
      Site_Provider: chargepoint?.provider,
      Site_Country: chargepoint?.site?.siteDetails?.country,
      ConnectorType: connector?.type,
      Connector_ID: connector?.connectorInternalId,
      CP_Scheme: chargepoint?.schemes[0]?.schemeName,
      CP_Operator: chargepoint?.site?.cpo,
      user_journey,
    });
  },
  [ChargeAnalyticsEvent.CHARGE_STOP_CHARGING_POP_UP_YES_CLICK]: ({
    chargepoint,
    connector,
    user_journey,
  }) => {
    return logCustomEvent('Charge_StopChargingPopUp_Yes_Click', {
      CP_ID: chargepoint?.apolloInternalId,
      Site_Provider: chargepoint?.provider,
      Site_Country: chargepoint?.site?.siteDetails?.country,
      ConnectorType: connector?.type,
      Connector_ID: connector?.connectorInternalId,
      CP_Scheme: chargepoint?.schemes[0]?.schemeName,
      CP_Operator: chargepoint?.site?.cpo,
      user_journey,
    });
  },
  [ChargeAnalyticsEvent.CHARGE_STOP_CHARGING_POP_UP_NO_CLICK]: ({
    chargepoint,
    connector,
    user_journey,
  }) => {
    return logCustomEvent('Charge_StopChargingPopUp_No_Click', {
      CP_ID: chargepoint?.apolloInternalId,
      Site_Provider: chargepoint?.provider,
      Site_Country: chargepoint?.site?.siteDetails?.country,
      ConnectorType: connector?.type,
      Connector_ID: connector?.connectorInternalId,
      CP_Scheme: chargepoint?.schemes[0]?.schemeName,
      CP_Operator: chargepoint?.site?.cpo,
      user_journey,
    });
  },
  [ChargeAnalyticsEvent.CHARGE_DISCONNECTING_SCREEN_OPEN]: ({
    chargepoint,
    connector,
    user_journey,
  }) => {
    return logCustomEvent('Charge_ChargeDisconnectingScreen_Open', {
      CP_ID: chargepoint?.apolloInternalId,
      Site_Provider: chargepoint?.provider,
      Site_Country: chargepoint?.site?.siteDetails?.country,
      ConnectorType: connector?.type,
      Connector_ID: connector?.connectorInternalId,
      CP_Scheme: chargepoint?.schemes[0]?.schemeName,
      CP_Operator: chargepoint?.site?.cpo,
      user_journey,
    });
  },
  [ChargeAnalyticsEvent.CHARGE_STOP_CHARGE_REQUEST_SUCCESSFUL]: ({
    chargepoint,
    connector,
    user_journey,
  }) => {
    return logCustomEvent('Charge_StopChargeRequest_Successful', {
      CP_ID: chargepoint?.apolloInternalId,
      Site_Provider: chargepoint?.provider,
      Site_Country: chargepoint?.site?.siteDetails?.country,
      ConnectorType: connector?.type,
      Connector_ID: connector?.connectorInternalId,
      CP_Scheme: chargepoint?.schemes[0]?.schemeName,
      CP_Operator: chargepoint?.site?.cpo,
      user_journey,
    });
  },
  [ChargeAnalyticsEvent.CHARGE_STOP_CHARGE_REQUEST_UNSUCCESSFUL]: ({
    chargepoint,
    connector,
    ErrorMessage,
    user_journey,
  }) => {
    return logCustomEvent('Charge_StopChargeRequest_Unsuccessful', {
      CP_ID: chargepoint?.apolloInternalId,
      Site_Provider: chargepoint?.provider,
      Site_Country: chargepoint?.site?.siteDetails?.country,
      ConnectorType: connector?.type,
      Connector_ID: connector?.connectorInternalId,
      ErrorMessage,
      CP_Scheme: chargepoint?.schemes[0]?.schemeName,
      CP_Operator: chargepoint?.site?.cpo,
      user_journey,
    });
  },
  [ChargeAnalyticsEvent.CHARGE_SELECT_CONNECTOR_NAVIGATE_TO_WEBSHOP]: ({
    chargepoint,
    connector,
    user_journey,
  }) => {
    return logCustomEvent('Charge_SelectConnector_NavigateToWebshop', {
      CP_ID: chargepoint?.apolloInternalId,
      Site_Provider: chargepoint?.provider,
      Site_Country: chargepoint?.site?.siteDetails?.country,
      ConnectorType: connector?.type,
      CP_Scheme: chargepoint?.schemes[0]?.schemeName,
      CP_Operator: chargepoint?.site?.cpo,
      Connector_ID: connector?.connectorExternalId,
      user_journey,
    });
  },
  [ChargeAnalyticsEvent.CHARGE_SELECT_CONNECTOR_OUTSIDE_REGION]: ({
    chargepoint,
    connector,
  }) => {
    return logCustomEvent('Charge_SelectConnector_OutsideRegion', {
      CP_ID: chargepoint?.apolloInternalId,
      Site_Provider: chargepoint?.provider,
      Site_Country: chargepoint?.site?.siteDetails?.country,
      ConnectorType: connector?.type,
      CP_Scheme: chargepoint?.schemes[0]?.schemeName,
      CP_Operator: chargepoint?.site?.cpo,
    });
  },
  [ChargeAnalyticsEvent.CHARGE_SELECT_CONNECTOR_CHARGE_CLICK]: ({
    chargepoint,
    connector,
  }) => {
    return logCustomEvent('Charge_SelectConnector_ChargeClick', {
      CP_ID: chargepoint?.apolloInternalId,
      Site_Provider: chargepoint?.provider,
      Site_Country: chargepoint?.site?.siteDetails?.country,
      ConnectorType: connector?.type,
      ConnectorID: connector?.connectorInternalId,
      CP_Scheme: chargepoint?.schemes[0]?.schemeName,
      CP_Operator: chargepoint?.site?.cpo,
    });
  },
  [ChargeAnalyticsEvent.CHARGE_SELECT_CONNECTOR_ADD_PAYMENT_METHOD]: ({
    chargepoint,
    connector,
    user_journey,
  }) => {
    return logCustomEvent('Charge_SelectConnector_AddPaymentMethod', {
      CP_ID: chargepoint?.apolloInternalId,
      Site_Provider: chargepoint?.provider,
      Site_Country: chargepoint?.site?.siteDetails?.country,
      ConnectorType: connector?.type,
      CP_Scheme: chargepoint?.schemes[0]?.schemeName,
      CP_Operator: chargepoint?.site?.cpo,
      user_journey,
    });
  },
  [ChargeAnalyticsEvent.CHARGE_MONITORING_GET_CHARGE_NO_DATA]: ({
    chargepoint,
    connector,
    user_journey,
  }) => {
    return logCustomEvent('Charge_Monitoring_GetCharge_NoData', {
      CP_ID: chargepoint?.apolloInternalId,
      Site_Provider: chargepoint?.provider,
      Site_Country: chargepoint?.site?.siteDetails?.country,
      ConnectorType: connector?.type,
      Connector_ID: connector?.connectorInternalId,
      CP_Scheme: chargepoint?.schemes[0]?.schemeName,
      CP_Operator: chargepoint?.site?.cpo,
      user_journey,
    });
  },

  [ChargeAnalyticsEvent.CHARGE_SERVICE_UNAVAILABLE_OPEN]: ({
    chargepoint,
    ErrorMessage,
    connector,
    user_journey,
  }) => {
    return logCustomEvent('Charge_ServiceUnavailable_Open', {
      CP_ID: chargepoint?.apolloInternalId,
      Site_Provider: chargepoint?.provider,
      Site_Country: chargepoint?.site?.siteDetails?.country,
      ErrorMessage,
      CP_Scheme: chargepoint?.schemes[0]?.schemeName,
      Connector_ID: connector?.connectorInternalId,
      user_journey,
    });
  },
  [ChargeAnalyticsEvent.CHARGE_STOP_CHARGE_ACCEPTED_SUCCESSFUL]: ({
    chargepoint,
    connector,
    user_journey,
  }) => {
    return logCustomEvent('Charge_StopChargeAccepted_Successful', {
      CP_ID: chargepoint?.apolloInternalId,
      Site_Provider: chargepoint?.provider,
      Site_Country: chargepoint?.site?.siteDetails?.country,
      ConnectorType: connector?.type,
      Connector_ID: connector?.connectorInternalId,
      CP_Scheme: chargepoint?.schemes[0]?.schemeName,
      CP_Operator: chargepoint?.site?.cpo,
      user_journey,
    });
  },
  [ChargeAnalyticsEvent.CHARGE_STOP_CHARGE_ACCEPTED_UNSUCCESSFUL]: ({
    chargepoint,
    connector,
    ErrorMessage,
    user_journey,
  }) => {
    return logCustomEvent('Charge_StopChargeAccepted_Unsuccessful', {
      CP_ID: chargepoint?.apolloInternalId,
      Site_Provider: chargepoint?.provider,
      Site_Country: chargepoint?.site?.siteDetails?.country,
      ConnectorType: connector?.type,
      Connector_ID: connector?.connectorInternalId,
      ErrorMessage,
      CP_Scheme: chargepoint?.schemes[0]?.schemeName,
      CP_Operator: chargepoint?.site?.cpo,
      user_journey,
    });
  },
  [ChargeAnalyticsEvent.CHARGE_STOP_CHARGE_SUCCESSFUL]: ({
    chargepoint,
    connector,
    user_journey,
  }) => {
    return logCustomEvent('Charge_StopChargeSuccessful', {
      CP_ID: chargepoint?.apolloInternalId,
      Site_Provider: chargepoint?.provider,
      Site_Country: chargepoint?.site?.siteDetails?.country,
      ConnectorType: connector?.type,
      Connector_ID: connector?.connectorInternalId,
      CP_Scheme: chargepoint?.schemes[0]?.schemeName,
      CP_Operator: chargepoint?.site?.cpo,
      user_journey,
    });
  },
  [ChargeAnalyticsEvent.CHARGE_STOP_CHARGE_RESPONSE_UNSUCCESSFUL]: ({
    chargepoint,
    connector,
    ErrorMessage,
    user_journey,
  }) => {
    return logCustomEvent('Charge_StopChargeResponse_Unsuccessful', {
      CP_ID: chargepoint?.apolloInternalId,
      Site_Provider: chargepoint?.provider,
      Site_Country: chargepoint?.site?.siteDetails?.country,
      ConnectorType: connector?.type,
      Connector_ID: connector?.connectorInternalId,
      ErrorMessage,
      CP_Scheme: chargepoint?.schemes[0]?.schemeName,
      CP_Operator: chargepoint?.site?.cpo,
      user_journey,
    });
  },
  [ChargeAnalyticsEvent.CHARGE_DISCONNECTING_TIMEOUT_OPEN]: ({
    chargepoint,
    connector,
    ErrorMessage,
    user_journey,
  }) => {
    return logCustomEvent('Charge_DisconnectingTimeout_Open', {
      CP_ID: chargepoint?.apolloInternalId,
      Site_Provider: chargepoint?.provider,
      Site_Country: chargepoint?.site?.siteDetails?.country,
      ConnectorType: connector?.type,
      Connector_ID: connector?.connectorInternalId,
      ErrorMessage,
      CP_Scheme: chargepoint?.schemes[0]?.schemeName,
      CP_Operator: chargepoint?.site?.cpo,
      user_journey,
    });
  },
  [ChargeAnalyticsEvent.CHARGE_STOP_CHARGE_UNSUCCESSFUL]: ({
    chargepoint,
    connector,
    ErrorMessage,
    user_journey,
  }) => {
    return logCustomEvent('Charge_StopChargeUnsuccessful', {
      CP_ID: chargepoint?.apolloInternalId,
      Site_Provider: chargepoint?.provider,
      Site_Country: chargepoint?.site?.siteDetails?.country,
      ConnectorType: connector?.type,
      Connector_ID: connector?.connectorInternalId,
      ErrorMessage,
      CP_Scheme: chargepoint?.schemes[0]?.schemeName,
      CP_Operator: chargepoint?.site?.cpo,
      user_journey,
    });
  },
  [ChargeAnalyticsEvent.CHARGE_SUMMARY_SCREEN_OPEN]: ({
    chargepoint,
    connector,
    user_journey,
  }) => {
    return logCustomEvent('Charge_SummaryScreen_Open', {
      CP_ID: chargepoint?.apolloInternalId,
      Site_Provider: chargepoint?.provider,
      Site_Country: chargepoint?.site?.siteDetails?.country,
      ConnectorType: connector?.type,
      Connector_ID: connector?.connectorInternalId,
      CP_Scheme: chargepoint?.schemes[0]?.schemeName,
      CP_Operator: chargepoint?.site?.cpo,
      user_journey,
    });
  },
  [ChargeAnalyticsEvent.CHARGE_PRE_AUTH_VALUE_DISPLAY_ERROR_OPEN]: ({
    chargepoint,
    connector,
    error_message,
    user_journey,
  }) => {
    return logCustomEvent('Charge_PreAuthValueDisplayError_Open', {
      CP_ID: chargepoint?.apolloInternalId,
      Site_Provider: chargepoint?.provider,
      Site_Country: chargepoint?.site?.siteDetails?.country,
      ConnectorType: connector?.type,
      Connector_ID: connector?.connectorInternalId,
      CP_Scheme: chargepoint?.schemes[0]?.schemeName,
      CP_Operator: chargepoint?.site?.cpo,
      error_message,
      user_journey,
    });
  },
  [ChargeAnalyticsEvent.CHARGE_SUMMARY_SCREEN_VAT_INVOICE_DOWNLOAD]: ({
    chargepoint,
    connector,
    user_journey,
  }) => {
    return logCustomEvent('Charge_SummaryScreen_VATInvoice_Download', {
      CP_ID: chargepoint?.apolloInternalId,
      Site_Provider: chargepoint?.provider,
      Site_Country: chargepoint?.site?.siteDetails?.country,
      CP_Scheme: chargepoint?.schemes[0]?.schemeName,
      CP_Operator: chargepoint?.site?.cpo,
      Connector_ID: connector?.connectorInternalId,
      user_journey,
    });
  },
  [ChargeAnalyticsEvent.CHARGE_SUMMARY_SCREEN_VAT_IN_HISTORY_CLICK]: ({
    chargepoint,
    connector,
    user_journey,
  }) => {
    return logCustomEvent('Charge_SummaryScreen_VATInHistory_Click', {
      CP_ID: chargepoint?.apolloInternalId,
      Site_Provider: chargepoint?.provider,
      Site_Country: chargepoint?.site?.siteDetails?.country,
      CP_Scheme: chargepoint?.schemes[0]?.schemeName,
      CP_Operator: chargepoint?.site?.cpo,
      Connector_ID: connector?.connectorInternalId,
      user_journey,
    });
  },
  [ChargeAnalyticsEvent.CHARGE_SUMMARY_ERROR_SCREEN_OPEN]: ({
    chargepoint,
    ErrorMessage,
    connector,
    user_journey,
  }) => {
    return logCustomEvent('Charge_SummaryErrorScreen_Open', {
      CP_ID: chargepoint?.apolloInternalId,
      Site_Provider: chargepoint?.provider,
      Site_Country: chargepoint?.site?.siteDetails?.country,
      ErrorMessage,
      CP_Scheme: chargepoint?.schemes[0]?.schemeName,
      CP_Operator: chargepoint?.site?.cpo,
      Connector_ID: connector?.connectorInternalId,
      user_journey,
    });
  },
  [ChargeAnalyticsEvent.CHARGE_GUEST_START_CHARGE_SELECT_PAYMENT]: ({
    chargepoint,
  }) => {
    return logCustomEvent('Charge_GuestStartCharge_SelectPayment', {
      CP_ID: chargepoint?.apolloInternalId,
      Site_Provider: chargepoint?.provider,
      Site_Country: chargepoint?.site?.siteDetails?.country,
      CP_Scheme: chargepoint?.schemes[0]?.schemeName,
    });
  },
  [ChargeAnalyticsEvent.CHARGE_PAYMENT_REQUIRED_SCREEN_OPEN]: ({
    user_journey,
  }) => {
    return logCustomEvent('Charge_PaymentRequiredScreen_Open', {
      user_journey,
    });
  },
  [ChargeAnalyticsEvent.CHARGE_PAYMENT_REQUIRED_SCREEN_CLOSE]: ({
    user_journey,
  }) => {
    return logCustomEvent('Charge_PaymentRequiredScreen_Close', {
      user_journey,
    });
  },
  [ChargeAnalyticsEvent.CHARGE_PAYMENT_REQUIRED_HISTORY_CLICK]: ({
    user_journey,
  }) => {
    return logCustomEvent('Charge_PaymentRequired_History_Click', {
      user_journey,
    });
  },
  [ChargeAnalyticsEvent.SERIAL_SEARCH_SCREEN_OPEN]: ({
    isLocationServicesEnabled,
    user_journey,
  }) => {
    return logCustomEvent('Charge_SerialSearchScreen_Open', {
      isLocationServicesEnabled: isLocationServicesEnabled,
      user_journey,
    });
  },
  [ChargeAnalyticsEvent.SERIAL_SEARCH_ID_MATCH]: ({
    chargepoint,
    connector,
    connectorInput,
    isLocationServicesEnabled,
    user_journey,
  }) => {
    return logCustomEvent('Charge_SearchID_Match', {
      CP_ID: chargepoint?.apolloInternalId,
      Site_Provider: chargepoint?.provider,
      Site_Country: chargepoint?.site?.siteDetails?.country,
      Connector_Type: connector?.type,
      CP_Scheme: chargepoint?.schemes[0]?.schemeName,
      ConnectorInput: connectorInput,
      CP_Operator: chargepoint?.site?.cpo,
      isLocationServicesEnabled: isLocationServicesEnabled,
      user_journey,
    });
  },
  [ChargeAnalyticsEvent.SERIAL_SEARCH_NO_ID_MATCH]: ({
    ErrorMessage,
    connectorInput,
    isLocationServicesEnabled,
  }) => {
    return logCustomEvent('Charge_SearchID_NoMatch', {
      ErrorMessage: ErrorMessage,
      ConnectorInput: connectorInput,
      isLocationServicesEnabled: isLocationServicesEnabled,
    });
  },
  [ChargeAnalyticsEvent.SERIAL_SEARCH_NO_ACCESS]: ({
    ErrorMessage,
    chargepoint,
    connectorInput,
    isLocationServicesEnabled,
  }) => {
    return logCustomEvent('Charge_SearchID_NoAccess', {
      ErrorMessage: ErrorMessage,
      CP_ID: chargepoint?.apolloInternalId,
      Site_Country: chargepoint?.site?.siteDetails?.country,
      CP_Scheme: chargepoint?.schemes[0]?.schemeName,
      ConnectorInput: connectorInput,
      isLocationServicesEnabled: isLocationServicesEnabled,
    });
  },
  [ChargeAnalyticsEvent.SERIAL_SEARCH_CHARGER_ID_HELP_CLICK]: ({
    isLocationServicesEnabled,
  }) => {
    return logCustomEvent('Charge_SearchID_Help_Click', {
      isLocationServicesEnabled: isLocationServicesEnabled,
    });
  },
  [ChargeAnalyticsEvent.CHARGE_CHARGER_NO_LONGER_AVAILABLE_OPEN]: ({
    chargepoint,
    connector,
    user_journey,
  }) => {
    return logCustomEvent('Charge_ChargerNoLongerAvailable_Open', {
      CP_ID: chargepoint?.apolloInternalId,
      Site_Provider: chargepoint?.provider,
      Site_Country: chargepoint?.site?.siteDetails?.country,
      CP_Scheme: chargepoint?.schemes[0]?.schemeName,
      CP_Operator: chargepoint?.site?.cpo,
      Connector_ID: connector?.connectorInternalId,
      user_journey,
    });
  },
  [ChargeAnalyticsEvent.CHARGE_NEAR_BY_SITE_SELECT_LOCATION_CLICK]: ({
    site,
  }) => {
    return logCustomEvent('Charge_NearBySiteSelectLocation_Click', {
      Site_ID: site?.siteId,
      Site_Provider: site?.provider,
      Site_Country: site?.siteDetails?.country,
      CP_Scheme: site?.chargepoints
        ?.flatMap((c: { schemes: Array<{ schemeName: string }> }) => c.schemes)
        .map((s: { schemeName: string }) => s.schemeName)
        .join(','),
      CP_Operator: site?.cpo,
    });
  },
  [ChargeAnalyticsEvent.CHARGE_NEAR_BY_SITE_CHARGE_NOW_CLICK]: ({
    chargepoint,
    user_journey,
  }) => {
    return logCustomEvent('Charge_NearBySiteChargeNow_Click', {
      CP_ID: chargepoint?.apolloInternalId,
      Site_Provider: chargepoint?.provider,
      Site_Country: chargepoint?.site?.siteDetails?.country,
      CP_Scheme: chargepoint?.schemes[0]?.schemeName,
      CP_Operator: chargepoint?.site?.cpo,
      user_journey,
    });
  },
  [ChargeAnalyticsEvent.CHARGE_START_CHARGE_BPCM_TIMEOUT_OPEN]: ({
    chargepoint,
  }) => {
    return logCustomEvent('Charge_StartChargeBPCMTimeout_Open', {
      CP_ID: chargepoint?.apolloInternalId,
      Site_Provider: chargepoint?.provider,
      Site_Country: chargepoint?.site?.siteDetails?.country,
      CP_Scheme: chargepoint?.schemes[0]?.schemeName,
    });
  },
  [ChargeAnalyticsEvent.CHARGE_START_SCREEN_PREAUTH_CLICK]: undefined,
  [ChargeAnalyticsEvent.CHARGE_STOP_EXTERNAL_OPEN]: ({
    chargepoint,
    connector,
    user_journey,
  }) => {
    return logCustomEvent('Charge_ChargeStopExternal_Open', {
      CP_ID: chargepoint?.apolloInternalId,
      Site_Provider: chargepoint?.provider,
      Site_Country: chargepoint?.site?.siteDetails?.country,
      CP_Scheme: chargepoint?.schemes[0]?.schemeName,
      CP_Operator: chargepoint?.site?.cpo,
      Connector_ID: connector?.connectorInternalId,
      user_journey,
    });
  },
  [ChargeAnalyticsEvent.CHARGE_CONNECT_INSTRUCTIONS_CLICK]: ({
    chargepoint,
    user_journey,
  }) => {
    return logCustomEvent('Charge_Connect_InstructionsClick', {
      CP_ID: chargepoint?.apolloInternalId,
      Site_Provider: chargepoint?.provider,
      Site_Country: chargepoint?.site?.siteDetails?.country,
      CP_Scheme: chargepoint?.schemes[0]?.schemeName,
      CP_Operator: chargepoint?.site?.cpo,
      user_journey,
    });
  },
  [ChargeAnalyticsEvent.CHARGE_CONNECT_START_CHARGE_CLICK]: ({
    chargepoint,
    connector,
    user_journey,
  }) => {
    return logCustomEvent('Charge_Connect_StartChargeClick', {
      CP_ID: chargepoint?.apolloInternalId,
      Site_Provider: chargepoint?.provider,
      Site_Country: chargepoint?.site?.siteDetails?.country,
      CP_Scheme: chargepoint?.schemes[0]?.schemeName,
      CP_Operator: chargepoint?.site?.cpo,
      Connector_ID: connector?.connectorInternalId,
      user_journey,
    });
  },
  [ChargeAnalyticsEvent.CHARGE_SELECT_CONNECTOR_MAP_ERROR]: ({
    ErrorMessage,
  }) => {
    return logCustomEvent('Charge_Select_Connector_Map_Error', {
      ErrorMessage: ErrorMessage,
    });
  },
  [ChargeAnalyticsEvent.CHARGE_SELECT_CONNECTOR_OPEN]: ({
    chargepoint,
    user_journey,
  }) => {
    return logCustomEvent('Charge_Select_Connector_Open', {
      CP_ID: chargepoint?.apolloInternalId,
      Site_Provider: chargepoint?.provider,
      Site_Country: chargepoint?.site?.siteDetails?.country,
      CP_Scheme: chargepoint?.schemes[0]?.schemeName,
      CP_Operator: chargepoint?.site?.cpo,
      user_journey,
    });
  },
  [ChargeAnalyticsEvent.CHARGE_SELECT_CONNECTOR_NO_ACCESS]: ({
    chargepoint,
    user_journey,
  }) => {
    return logCustomEvent('Charge_Select_Connector_no_Access', {
      CP_ID: chargepoint?.apolloInternalId,
      Site_Provider: chargepoint?.provider,
      Site_Country: chargepoint?.site?.siteDetails?.country,
      CP_Scheme: chargepoint?.schemes[0]?.schemeName,
      user_journey,
    });
  },
  [ChargeAnalyticsEvent.CHARGE_SELECT_CONNECTOR_TOP_UP_CLICK]: ({
    chargepoint,
  }) => {
    return logCustomEvent('Charge_SelectConnector_TopUp_Click', {
      CP_ID: chargepoint?.apolloInternalId,
      Site_Provider: chargepoint?.provider,
      Site_Country: chargepoint?.site?.siteDetails?.country,
      CP_Scheme: chargepoint?.schemes[0]?.schemeName,
    });
  },
  [ChargeAnalyticsEvent.CHARGE_TOP_UP_SCREEN_TOP_UP_CLICK]: ({
    chargepoint,
  }) => {
    return logCustomEvent('Charge_TopUpScreen_TopUpClick', {
      CP_ID: chargepoint?.apolloInternalId,
      Site_Provider: chargepoint?.provider,
      Site_Country: chargepoint?.site?.siteDetails?.country,
      CP_Scheme: chargepoint?.schemes[0]?.schemeName,
    });
  },
  [ChargeAnalyticsEvent.CHARGE_GET_DIRECTIONS_CLICK]: ({
    chargepoint,
    user_journey,
  }) => {
    return logCustomEvent('Charge_GetDirections_Click', {
      CP_ID: chargepoint?.apolloInternalId,
      Site_Provider: chargepoint?.provider,
      Site_Country: chargepoint?.site?.siteDetails?.country,
      CP_Scheme: chargepoint?.schemes[0]?.schemeName,
      CP_Operator: chargepoint?.site?.cpo,
      user_journey,
    });
  },
  [ChargeAnalyticsEvent.CHARGE_SELECT_CONNECTOR_CHARGER_INFORMATION_CLICK]: ({
    chargepoint,
    user_journey,
  }) => {
    return logCustomEvent('Charge_ChargerInformation_Click', {
      CP_ID: chargepoint?.apolloInternalId,
      Site_Provider: chargepoint?.provider,
      Site_Country: chargepoint?.site?.siteDetails?.country,
      CP_Scheme: chargepoint?.schemes[0]?.schemeName,
      CP_Operator: chargepoint?.site?.cpo,
      user_journey,
    });
  },
  [ChargeAnalyticsEvent.CHARGE_BLOCK_START_CHARGE_NO_LOCATION_SERVICES]: ({
    user_journey,
  }) => {
    return logCustomEvent('Charge_NoGPSLocation_StartChargeBlocked', {
      user_journey,
    });
  },
  [ChargeAnalyticsEvent.CHARGE_BLOCK_START_CHARGE_USER_DISTANCE]: ({
    chargerId,
    distance,
    user_journey,
  }) => {
    return logCustomEvent('Charge_UserDistance_StartChargeBlocked', {
      chargerId,
      distance,
      user_journey,
    });
  },

  [GuestAnalyticsEvent.PRE_AUTHORISATION_FAILED]: ({ errorMessage }) => {
    return logCustomEvent('Guest_PreAuthorisationFailed', {
      errorMessage: errorMessage,
    });
  },
  [GuestAnalyticsEvent.PRE_AUTHORISATION_SUCCESSFUL]: () => {
    return logCustomEvent('Guest_PreAuthorisationSuccessful');
  },

  [RFIDAnalyticsEvent.PULSE_CHARGE_CARD_OPEN]: ({
    tag_notes,
    user_journey,
  }) => {
    return logCustomEvent('RFID_PulseChargeCard_Open', {
      user_journey,
      ...tag_notes,
    });
  },
  [RFIDAnalyticsEvent.CONFIRM_AND_ORDER_ORDER_CTA_CLICK]: ({
    tag_notes,
    user_journey,
  }) => {
    return logCustomEvent('RFID_ConfirmAndOrder_OrderCTA_Click', {
      user_journey,
      ...tag_notes,
    });
  },
  [RFIDAnalyticsEvent.CARD_ORDER_SUCCESS]: ({ user_journey, tag_notes }) => {
    return logCustomEvent('RFID_CardOrder_Success', {
      user_journey,
      ...tag_notes,
    });
  },
  [RFIDAnalyticsEvent.PULSE_CHARGE_CARD_ORDER_CTA_CLICK]: ({
    user_journey,
    tag_notes,
  }) => {
    return logCustomEvent('RFID_PulseChargeCard_OrderCTA_Click', {
      user_journey,
      ...tag_notes,
    });
  },
  [RFIDAnalyticsEvent.LINK_PAYMENT_CARD_SCREEN_OPEN]: ({
    user_journey,
    tag_notes,
  }) => {
    return logCustomEvent('RFID_LinkPaymentCardScreen_Open', {
      user_journey,
      ...tag_notes,
    });
  },
  [RFIDAnalyticsEvent.LINK_PAYMENT_CARD_SCREEN_CTA_CLICK]: ({
    user_journey,
    tag_notes,
  }) => {
    return logCustomEvent('RFID_LinkPaymentCardScreen_CTA_Click', {
      user_journey,
      ...tag_notes,
    });
  },
  [RFIDAnalyticsEvent.SHIPPING_ADDRESS_OPEN]: () => {
    return logCustomEvent('RFID_ShippingAddress_Open');
  },
  [RFIDAnalyticsEvent.CONFIRM_AND_ORDER_OPEN]: ({
    user_journey,
    tag_notes,
  }) => {
    return logCustomEvent('RFID_ConfirmAndOrder_Open', {
      user_journey,
      ...tag_notes,
    });
  },
  [RFIDAnalyticsEvent.CHARGE_CARD_SCREEN_OPEN]: ({ tag_notes }) => {
    return logCustomEvent('RFID_ChargeCardScreen_Open', {
      ...tag_notes,
    });
  },
  [RFIDAnalyticsEvent.CARD_ORDER_FAILED]: ({
    error_message,
    user_journey,
    tag_notes,
  }) => {
    return logCustomEvent('RFID_CardOrder_Failed', {
      error_message,
      user_journey,
      ...tag_notes,
    });
  },
  [RFIDAnalyticsEvent.CHARGE_CARD_SCREEN_REPLACE_CLICK]: () => {
    return logCustomEvent('RFID_ChargeCardScreen_Replace_Click');
  },
  [RFIDAnalyticsEvent.REPLACE_CARD_POP_UP_ORDER_NEW_SELECT]: () => {
    return logCustomEvent('RFID_ReplaceCardPopUp_OrderNew_Click');
  },
  [RFIDAnalyticsEvent.REPLACE_CARD_POP_UP_KEEP_CARD_SELECT]: () => {
    return logCustomEvent('RFID_ReplaceCardPopup_KeepCard_Click');
  },
  [RFIDAnalyticsEvent.REPLACE_CARD_SUCCESS]: () => {
    return logCustomEvent('RFID_ReplaceCard_ Success');
  },
  [RFIDAnalyticsEvent.REPLACE_CARD_FAILED]: ({ error_message }) => {
    return logCustomEvent('RFID_ReplaceCard_Failed', {
      error_message,
    });
  },
  [RFIDAnalyticsEvent.CHARGE_CARD_SCREEN_CANCEL_CLICK]: () => {
    return logCustomEvent('RFID_ChargeCardScreen_Cancel_Click');
  },
  [RFIDAnalyticsEvent.CANCEL_CARD_FAILED]: ({ error_message }) => {
    return logCustomEvent('RFID_CancelCard_Failed', {
      error_message,
    });
  },
  [RFIDAnalyticsEvent.CANCEL_CARD_SUCCESS]: () => {
    return logCustomEvent('RFID_CancelCard_Success');
  },
  [RFIDAnalyticsEvent.POPULATED_SHIPPING_ADDRESS_OPEN]: () => {
    return logCustomEvent('RFID_PopulatedShippingAddress_Open');
  },
  [RFIDAnalyticsEvent.POPULATED_SHIPPING_ADDRESS_SAVE_CLICK]: () => {
    return logCustomEvent('RFID_PopulatedShippingAddress_SaveClick');
  },
  [RFIDAnalyticsEvent.SHIPPING_ADDRESS_BACK_CLICK]: () => {
    return logCustomEvent('RFID_ShippingAddress_Back_Click');
  },
  [RFIDAnalyticsEvent.SHIPPING_ADDRESS_USE_ADDRESS_CLICK]: () => {
    return logCustomEvent('RFID_ShippingAddress_UseAddress_Click');
  },
  [RFIDAnalyticsEvent.SHIPPING_ADDRESS_ERROR]: ({ error_message }) => {
    return logCustomEvent('RFID_ShippingAddress_Error', {
      error_message,
    });
  },
  [RFIDAnalyticsEvent.UBER_MIGRATION_SCREEN_SUCCESS_OPEN]: ({
    migration_status,
  }) => {
    return logCustomEvent('UberMigrationScreen_Success_Open', {
      migration_status,
    });
  },

  [RTBFAnalyticsEvent.DELETE_ACCOUNT_SCREEN_OPEN]: () => {
    return logCustomEvent('RTBF_DeleteAccountScreen_Open');
  },
  [RTBFAnalyticsEvent.DELETE_ACCOUNT_SCREEN_BACK]: () => {
    return logCustomEvent('RTBF_DeleteAccountScreen_Back');
  },
  [RTBFAnalyticsEvent.DELETE_ACCOUNT_SCREEN_KEEP_CLICK]: () => {
    return logCustomEvent('RTBF_AccountScreenKeep_Click');
  },
  [RTBFAnalyticsEvent.DELETE_ACCOUNT_SCREEN_DELETE_CLICK]: () => {
    return logCustomEvent('RTBF_AccountScreenDelete_Click');
  },
  [RTBFAnalyticsEvent.REFUND_EMAIL_SENT]: () => {
    return logCustomEvent('RTBF_RefundEmail_Sent');
  },
  [RTBFAnalyticsEvent.DELETE_ACCOUNT_SCREEN_SETTINGS_CLICK]: () => {
    return logCustomEvent('RTBF_DeleteAccountScreen_Settings_Click	');
  },
  [RTBFAnalyticsEvent.CONFIRM_DELETION_SCREEN_BACK_CLICK]: () => {
    return logCustomEvent('RTBF_ConfirmDeletionScreen_Back_Click');
  },
  [RTBFAnalyticsEvent.CONFIRM_DELETION_SCREEN_KEEP_CLICK]: () => {
    return logCustomEvent('RTBF_ConfirmDeletionScreen_Keep_Click');
  },
  [RTBFAnalyticsEvent.CONFIRM_DELETION_CANCEL_SUBS_CLICK]: () => {
    return logCustomEvent('RTBF_ConfirmDeletion_CancelSubs_Click');
  },
  [RTBFAnalyticsEvent.CONFIRM_DELETION_SCREEN_CONFIRM_CLICK]: ({
    deletionReason,
  }) => {
    return logCustomEvent('RTBF_DeletionScreen_Confirm_Click', {
      deletionReason,
    });
  },
  [RTBFAnalyticsEvent.REQUEST_SENT_DONE]: () => {
    return logCustomEvent('RTBF_RequestSent_Done');
  },
  [RTBFAnalyticsEvent.REQUEST_FAILED_DONE]: ({ errorMessage }) => {
    return logCustomEvent('RTBF_RequestFailed_Done', { errorMessage });
  },
  [RTBFAnalyticsEvent.REQUEST_RECEIVED_OPEN]: () => {
    return logCustomEvent('RTBF_RequestReceived_Open');
  },
  [WalletAnalyticsEvent.ADD_CARD_SCREEN_RECORD_DETECTED_OPEN]: ({
    errorMessage,
  }) => {
    return logCustomEvent('Wallet_AddCard_ScreenRecordDetected_Open', {
      ErrorMessage: errorMessage,
    });
  },
  [WalletAnalyticsEvent.SUBS_CVV_RECACHE_FLOW_OPEN]: ({ user_journey }) => {
    return logCustomEvent('SUBS_CVVRecacheFlow_Open', {
      user_journey,
    });
  },
  [WalletAnalyticsEvent.SUBS_CVV_RECACHE_FLOW_COMPLETE]: ({ user_journey }) => {
    return logCustomEvent('SUBS_CVVRecacheFlow_Complete', {
      user_journey,
    });
  },
  [WalletAnalyticsEvent.CHANGE_DEFAULT_CARD_SAVE]: () => {
    return logCustomEvent('Wallet_ChangeDefaultCard_Save');
  },
  [WalletAnalyticsEvent.CHARGE_DEFAULT_CARD_UPDATE]: () => {
    return logCustomEvent('Wallet_Charge_DefaultCard_Update');
  },
  [WalletAnalyticsEvent.CHARGE_DEFAULT_CARD_ADD_NEW]: () => {
    return logCustomEvent('Wallet_Charge_DefaultCard_AddNew');
  },
  [WalletAnalyticsEvent.OVERDRAWN_BANNER_VIEW_CLICK]: () => {
    return logCustomEvent('Wallet_OverdrawnBanner_View_Click');
  },
  [WalletAnalyticsEvent.OVERDRAWN_FAILED_SCREEN_CLOSE]: () => {
    return logCustomEvent('Wallet_OverdrawnFailedScreen_Close');
  },
  [WalletAnalyticsEvent.OVERDRAWN_FAILED_SCREEN_OPEN]: ({ errorMessage }) => {
    return logCustomEvent('Wallet_OverdrawnFailedScreen_Open', {
      ErrorMessage: errorMessage,
    });
  },
  [WalletAnalyticsEvent.OVERDRAWN_SUCCESS_SCREEN_FINISH]: () => {
    return logCustomEvent('Wallet_OverdrawnSuccessScreen_Finish');
  },
  [WalletAnalyticsEvent.OVERDRAWN_SUCCESS_SCREEN_OPEN]: () => {
    return logCustomEvent('Wallet_OverdrawnSuccessScreen_Open');
  },
  [WalletAnalyticsEvent.OVERDRAWN_SUM_SCREEN_AGREE_CLICK]: () => {
    return logCustomEvent('Wallet_OverdrawnSumScreen_Agree_Click');
  },
  [WalletAnalyticsEvent.CARD_ADDED_SCREEN_FINISH_CLICK]: () => {
    return logCustomEvent('Wallet_CardAddedScreen_Finish_Click');
  },
  [WalletAnalyticsEvent.CARD_ADDED_SCREEN_OPEN]: () => {
    return logCustomEvent('Wallet_CardAddedScreen_Open');
  },
  [WalletAnalyticsEvent.PAYMENT_DETAILS_OPEN]: () => {
    return logCustomEvent('Wallet_PaymentDetails_Open');
  },
  [WalletAnalyticsEvent.PAYMENT_DETAILS_ADD_NEW_CARD_CLICK]: () => {
    return logCustomEvent('Wallet_PaymentDetails_AddNewCard_Click');
  },
  [WalletAnalyticsEvent.PAYMENT_DETAILS_MY_SUBS_CLICK]: () => {
    return logCustomEvent('Wallet_PaymentDetails_MySubs_Click');
  },
  [WalletAnalyticsEvent.PAYMENT_SCREEN_RECORD_DETECTED_OPEN]: ({
    errorMessage,
  }) => {
    return logCustomEvent('Wallet_Payment_ScreenRecordDetected_Open', {
      ErrorMessage: errorMessage,
    });
  },
  [WalletAnalyticsEvent.PRE_AUTH_SETUP_FAILURE_CLOSE_CLICK]: ({
    CP_ID,
    Site_Provider,
    Site_Country,
    ConnectorType,
    CP_Scheme,
    CP_Operator,
    Connector_ID,
  }) => {
    return logCustomEvent('Wallet_PreAuthSetupFailure_Close_Click', {
      CP_ID,
      Site_Provider,
      Site_Country,
      ConnectorType,
      CP_Scheme,
      CP_Operator,
      Connector_ID,
    });
  },
  [WalletAnalyticsEvent.PRE_AUTH_SETUP_FAILURE_OPEN]: ({
    CP_ID,
    Site_Provider,
    Site_Country,
    ConnectorType,
    CP_Scheme,
    CP_Operator,
    Connector_ID,
    error_message,
  }) => {
    return logCustomEvent('Wallet_PreAuthSetupFailure_Open', {
      CP_ID,
      Site_Provider,
      Site_Country,
      ConnectorType,
      CP_Scheme,
      CP_Operator,
      Connector_ID,
      error_message,
    });
  },
  [WalletAnalyticsEvent.PRE_AUTH_SETUP_FAILURE_RETRY_CLICK]: ({
    CP_ID,
    Site_Provider,
    Site_Country,
    ConnectorType,
    CP_Scheme,
    CP_Operator,
    Connector_ID,
  }) => {
    return logCustomEvent('Wallet_PreAuthSetupFailure_Retry_Click', {
      CP_ID,
      Site_Provider,
      Site_Country,
      ConnectorType,
      CP_Scheme,
      CP_Operator,
      Connector_ID,
    });
  },
  [WalletAnalyticsEvent.PRE_AUTH_SETUP_SUCCESS]: ({
    CP_ID,
    Site_Provider,
    Site_Country,
    ConnectorType,
    CP_Scheme,
    CP_Operator,
    Connector_ID,
  }) => {
    return logCustomEvent('Wallet_PreAuthSetup_Success', {
      CP_ID,
      Site_Provider,
      Site_Country,
      ConnectorType,
      CP_Scheme,
      CP_Operator,
      Connector_ID,
    });
  },
  [WalletAnalyticsEvent.REMOVE_CARD_ERROR_POP_UP_OPEN]: ({
    availablePaymentMethods,
    errorMessage,
  }) => {
    return logCustomEvent('Wallet_RemoveCard_ErrorPopUp_Open', {
      ErrorMessage: errorMessage,
      AvailablePaymentMethods: availablePaymentMethods,
    });
  },
  [WalletAnalyticsEvent.REMOVE_CARD_CANT_REMOVE_POP_UP_OPEN]: ({
    availablePaymentMethods,
    errorMessage,
  }) => {
    return logCustomEvent('Wallet_RemoveCard_CantRemovePopUp_Open', {
      ErrorMessage: errorMessage,
      AvailablePaymentMethods: availablePaymentMethods,
    });
  },
  [WalletAnalyticsEvent.REMOVE_FINAL_CARD_POP_UP_REMOVE_CLICK]: ({
    availablePaymentMethods,
  }) => {
    return logCustomEvent('Wallet_RemoveFinalCardPopUp_Remove_Click', {
      AvailablePaymentMethods: availablePaymentMethods,
    });
  },
  [WalletAnalyticsEvent.REMOVE_CARD_POP_UP_REMOVE_CLICK]: ({
    availablePaymentMethods,
  }) => {
    return logCustomEvent('Wallet_RemoveCardPopUp_Remove_Click', {
      AvailablePaymentMethods: availablePaymentMethods,
    });
  },
  [WalletAnalyticsEvent.UNABLE_TO_ADD_CARD_SCREEN_CLOSE_CLICK]: () => {
    return logCustomEvent('Wallet_UnableToAddCardScreen_Close_Click');
  },
  [WalletAnalyticsEvent.UNABLE_TO_ADD_CARD_SCREEN_OPEN]: ({ errorMessage }) => {
    return logCustomEvent('Wallet_UnableToAddCardScreen_Open', {
      ErrorMessage: errorMessage,
    });
  },
  [WalletAnalyticsEvent.UNABLE_TO_ADD_CARD_SCREEN_RETRY_CLICK]: ({
    errorMessage,
  }) => {
    return logCustomEvent('Wallet_UnableToAddCardScreen_Retry_Click', {
      ErrorMessage: errorMessage,
    });
  },
  [WalletAnalyticsEvent.CVV_RECACHE_FLOW_COMPLETE_3DS_CHECK_FAILURE]: ({
    errorMessage,
  }) => {
    return logCustomEvent('Wallet_CVVRecacheFlow_3DSCheck_Failure', {
      ErrorMessage: errorMessage,
    });
  },
  [WalletAnalyticsEvent.EXPOSED_COMPONENT_ERROR_OPEN]: ({ errorMessage }) => {
    return logCustomEvent('Wallet_ExposedComponent_Error_Open', {
      ErrorMessage: errorMessage,
    });
  },
  [WalletAnalyticsEvent.EXPOSED_COMPONENT_ERROR_RETRY_CLICK]: ({
    errorMessage,
  }) => {
    return logCustomEvent('Wallet_ExposedComponent_Error_RetryClick', {
      ErrorMessage: errorMessage,
    });
  },
  [CreditAnalyticsEvent.CREDIT_YOUR_CREDIT_SCREEN_OPEN]: () => {
    return logCustomEvent('Credit_YourCreditScreen_Open');
  },
  [CreditAnalyticsEvent.CREDIT_YOUR_CREDIT_SCREEN_ERROR_VIEW]: ({
    errorMessage,
    creditBalance,
  }) => {
    return logCustomEvent('Credit_YourCreditScreen_Error_View', {
      errorMessage,
      creditBalance,
    });
  },
  [CreditAnalyticsEvent.CREDIT_TOP_UP_CREDIT_BUTTON_CLICK]: ({
    creditBalance,
  }) => {
    return logCustomEvent('Credit_TopUpCreditButton_Click', { creditBalance });
  },
  [CreditAnalyticsEvent.CREDIT_TOP_UP_CREDIT_SUCCESS]: ({ creditBalance }) => {
    return logCustomEvent('Credit_TopUpCredit_Success', { creditBalance });
  },
  [CreditAnalyticsEvent.CREDIT_TOP_UP_CREDIT_FAILED]: ({ errorMessage }) => {
    return logCustomEvent('Credit_TopUpCredit_Failed', { errorMessage });
  },
  [CreditAnalyticsEvent.CREDIT_GET_CUSTOMER_SUCCESS]: ({ creditBalance }) => {
    return logCustomEvent('Credit_GetCustomer_Success', { creditBalance });
  },
  [CreditAnalyticsEvent.CREDIT_GET_CUSTOMER_ERROR]: ({ errorMessage }) => {
    return logCustomEvent('Credit_GetCustomer_Error', { errorMessage });
  },
  [CreditAnalyticsEvent.CREDIT_STRIPE_WEBVIEW_CLOSED]: undefined,

  [SubsAnalyticsEvent.SUBS_WALLET_ADD_CARD_FAILURE]: ({ errorMessage }) => {
    return logCustomEvent('SUBS_WalletAddCard_Failure', {
      errorMessage,
    });
  },
  [SubsAnalyticsEvent.SUBS_WALLET_ADD_CARD_SUCCESS]: ({
    first_time_subscribing,
  }) => {
    return logCustomEvent('SUBS_WalletAddCard_Success', {
      first_time_subscribing,
    });
  },
  [SubsAnalyticsEvent.SUBS_INTRO_OFFER_SCREEN_OPEN]: ({
    account_balance,
    gocardless_mandate_status,
    tag_ids,
  }) => {
    return logCustomEvent('Subs_IntroOfferScreen_Open', {
      account_balance,
      gocardless_mandate_status,
      tag_ids,
    });
  },
  [SubsAnalyticsEvent.SUBS_REACTIVATE_SUBS_SCREEN_OPEN]: ({
    account_balance,
    gocardless_mandate_status,
    tag_ids,
  }) => {
    return logCustomEvent('Subs_ReactiveSubsScreen_Open', {
      account_balance,
      gocardless_mandate_status,
      tag_ids,
    });
  },
  [SubsAnalyticsEvent.SUBS_RFID_PREFERENCE_SCREEN_OPEN]: ({
    account_balance,
    gocardless_mandate_status,
    tag_ids,
  }) => {
    return logCustomEvent('Subs_RFIDPreferenceScreen_Open', {
      account_balance,
      gocardless_mandate_status,
      tag_ids,
    });
  },
  [SubsAnalyticsEvent.SUBS_ENTER_ADDRESS_SCREEN_OPEN]: ({
    account_balance,
    gocardless_mandate_status,
    tag_ids,
  }) => {
    return logCustomEvent('Subs_EnterAddressScreen_Open', {
      account_balance,
      gocardless_mandate_status,
      tag_ids,
    });
  },
  [SubsAnalyticsEvent.SUBS_ADD_OFFER_CODE_SCREEN_OPEN]: ({
    account_balance,
    gocardless_mandate_status,
    tag_ids,
  }) => {
    return logCustomEvent('Subs_AddOfferCodeScreen_Open', {
      account_balance,
      gocardless_mandate_status,
      tag_ids,
    });
  },
  [SubsAnalyticsEvent.SUBS_ADD_OFFER_CODE_APPLY_SUCCESS]: ({
    account_balance,
    gocardless_mandate_status,
    tag_ids,
  }) => {
    return logCustomEvent('Subs_AddOfferCodeApply_Success', {
      account_balance,
      gocardless_mandate_status,
      tag_ids,
    });
  },
  [SubsAnalyticsEvent.SUBS_ADD_OFFER_CODE_APPLY_ERROR]: ({ errorMessage }) => {
    return logCustomEvent('Subs_AddOfferCode_ApplyError', { errorMessage });
  },
  [SubsAnalyticsEvent.SUBS_SETUP_DIRECT_DEBIT_SCREEN_OPEN]: ({
    account_balance,
    gocardless_mandate_status,
    tag_ids,
  }) => {
    return logCustomEvent('Subs_SetupDirectDebitScreen_Open', {
      account_balance,
      gocardless_mandate_status,
      tag_ids,
    });
  },
  [SubsAnalyticsEvent.SUBS_SETUP_COMPLETE]: ({
    account_balance,
    gocardless_mandate_status,
    tag_ids,
    first_time_subscribing,
    offer_code,
    currency,
    value,
    discount,
    subscription_id,
  }) => {
    return logCustomEvent('Subs_SetupComplete', {
      account_balance,
      gocardless_mandate_status,
      tag_ids,
      first_time_subscribing,
      offer_code,
      currency,
      value,
      discount,
      subscription_id,
    });
  },
  [SubsAnalyticsEvent.SUBS_PENDING_SCREEN_OPEN]: ({
    account_balance,
    gocardless_mandate_status,
    tag_ids,
  }) => {
    return logCustomEvent('Subs_PendingScreen_Open', {
      account_balance,
      gocardless_mandate_status,
      tag_ids,
    });
  },
  [SubsAnalyticsEvent.SUBS_CANCEL_SUBSCRIPTION_CLICK]: () => {
    return logCustomEvent('Subs_CancelSubscription_Click', {});
  },

  [SubsAnalyticsEvent.SUBS_SUBSCRIBE_AND_SAVE_SCREEN_OPEN]: ({
    tag_ids,
    first_time_subscribing,
  }) => {
    return logCustomEvent('SUBS_SubscribeAndSaveScreen_Open', {
      tag_ids,
      first_time_subscribing,
    });
  },
  [SubsAnalyticsEvent.SUBS_SUBSCRIBE_AND_SAVE_SCREEN_CTA_CLICK]: ({
    tag_ids,
    first_time_subscribing,
    payment_methods,
  }) => {
    return logCustomEvent('SUBS_SubscribeAndSaveScreenCTA_Click', {
      tag_ids,
      first_time_subscribing,
      payment_methods,
    });
  },
  [SubsAnalyticsEvent.SUBS_MY_SUBSCRIPTION_SCREEN_OPEN]: () => {
    return logCustomEvent('SUBS_MySubscriptionScreen_Open', {});
  },
  [SubsAnalyticsEvent.SUBS_CONFIRM_SUBSCRIPTION_OPEN]: ({
    first_time_subscribing,
  }) => {
    return logCustomEvent('SUBS_ConfirmSubscription_Open', {
      first_time_subscribing,
    });
  },
  [SubsAnalyticsEvent.SUBS_OFFER_CODE_CHECK_CLICK]: ({
    entered_value,
    first_time_subscribing,
  }) => {
    return logCustomEvent('SUBS_OfferCodeCheck_Click', {
      entered_value,
      first_time_subscribing,
    });
  },
  [SubsAnalyticsEvent.SUBS_OFFER_CODE_CHECK_SUCCESS]: ({
    entered_value,
    first_time_subscribing,
  }) => {
    return logCustomEvent('SUBS_OfferCodeCheck_Success', {
      entered_value,
      first_time_subscribing,
    });
  },
  [SubsAnalyticsEvent.SUBS_OFFER_CODE_CHECK_ERROR]: ({
    error_message,
    entered_value,
  }) => {
    return logCustomEvent('SUBS_OfferCodeCheck_Error', {
      error_message,
      entered_value,
    });
  },
  [SubsAnalyticsEvent.SUBS_CONFIRM_AND_AUTHORISE_CLICK]: ({
    tag_ids,
    first_time_subscribing,
    offer_code,
  }) => {
    return logCustomEvent('SUBS_ConfirmAndAuthorise_Click', {
      tag_ids,
      first_time_subscribing,
      offer_code,
    });
  },
  [SubsAnalyticsEvent.SUBS_SUCCESS_SCREEN_START_CHARGING_CLICK]: ({
    tag_ids,
    first_time_subscribing,
    offer_code,
  }) => {
    return logCustomEvent('SUBS_SuccessScreen_StartCharging_Click', {
      tag_ids,
      first_time_subscribing,
      offer_code,
    });
  },
  [SubsAnalyticsEvent.SUBS_SUCCESS_SCREEN_ORDER_RFID_CLICK]: ({
    offer_code,
    first_time_subscribing,
  }) => {
    return logCustomEvent('SUBS_SuccessScreen_OrderRFID_Click', {
      offer_code,
      first_time_subscribing,
    });
  },
  [SubsAnalyticsEvent.SUBS_SETUP_FAILURE_OPEN]: ({
    entered_value,
    first_time_subscribing,
    error_message,
  }) => {
    return logCustomEvent('SUBS_SetupFailure_Open ', {
      entered_value,
      first_time_subscribing,
      error_message,
    });
  },
  [SubsAnalyticsEvent.SUBS_SETUP_FAILURE_RETRY_CLICK]: ({
    entered_value,
    first_time_subscribing,
  }) => {
    return logCustomEvent('SUBS_SetupFailure_Retry_Click', {
      entered_value,
      first_time_subscribing,
    });
  },
  [SubsAnalyticsEvent.SUBS_UNPAID_SUBSCRIPTION_EXIT_CLICK]: ({ tag_ids }) => {
    return logCustomEvent('SUBS_UnpaidSubscriptionScreen_ExitClick', {
      tag_ids,
    });
  },
  [SubsAnalyticsEvent.SUBS_UNPAID_SUBSCRIPTION_SCREEN_OPEN]: ({ tag_ids }) => {
    return logCustomEvent('SUBS_UnpaidSubscriptionScreen_Open', {
      tag_ids,
    });
  },
  [SubsAnalyticsEvent.SUBS_UNPAID_SUBSCRIPTION_SCREEN_RETRY_CLICK]: ({
    tag_ids,
  }) => {
    return logCustomEvent('SUBS_UnpaidSubscriptionScreen_RetryClick', {
      tag_ids,
    });
  },
  [SubsAnalyticsEvent.SUBS_UNPAID_SUBS_RETRY_PAYMENT_FAILURE]: ({
    error_message,
  }) => {
    return logCustomEvent('SUBS_UnpaidSubs_RetryPayment_Failure', {
      error_message,
    });
  },
  [SubsAnalyticsEvent.SUBS_UNPAID_SUBS_RETRY_PAYMENT_SUCCESS]: ({
    tag_ids,
  }) => {
    return logCustomEvent('SUBS_UnpaidSubs_RetryPayment_Success', {
      tag_ids,
    });
  },
  [SubsAnalyticsEvent.SUBS_UNPAID_SUBS_DOWNGRADE_FAILURE]: ({
    error_message,
  }) => {
    return logCustomEvent('SUBS_UnpaidSubs_Downgrade_Failure', {
      error_message,
    });
  },
  [SubsAnalyticsEvent.SUBS_UNPAID_SUBS_DOWNGRADE_SUCCESS]: ({ tag_ids }) => {
    return logCustomEvent('SUBS_UnpaidSubs_Downgrade_Success', {
      tag_ids,
    });
  },
  [SubsAnalyticsEvent.SUBS_CANCEL_SUBSCRIPTION_COMPLETE]: ({
    account_balance,
    gocardless_mandate_status,
    tag_ids,
    offer_code,
  }) => {
    return logCustomEvent('Subs_CancelSubscription_Complete', {
      account_balance,
      gocardless_mandate_status,
      tag_ids,
      offer_code,
    });
  },
  [SubsAnalyticsEvent.SUBS_MEMBERSHIP_APPLY_AN_OFFER_CODE_CLICK]: ({
    account_balance,
    gocardless_mandate_status,
    tag_ids,
  }) => {
    return logCustomEvent('Subs_MembershipApplyAnOfferCode_Click', {
      account_balance,
      gocardless_mandate_status,
      tag_ids,
    });
  },
  [SubsAnalyticsEvent.SUBS_UPGRADE_PAYMENTREQUIRED_OPEN]: ({
    account_balance,
    gocardless_mandate_status,
    tag_ids,
  }) => {
    return logCustomEvent('Subs_MembershipApplyAnOfferCode_Click', {
      account_balance,
      gocardless_mandate_status,
      tag_ids,
    });
  },

  [RegAnalyticsEvent.CUSTOMISE_BP_PULSE_APP_OPEN]: () =>
    logCustomEvent('Reg_Customisebppulseapp_Open'),
  [RegAnalyticsEvent.CUSTOMISE_BP_PULSE_APP_EXIT_CLICK]: () =>
    logCustomEvent('Reg_Customisebppulseapp_ExitClick'),
  [RegAnalyticsEvent.EXIT_BACK_TOBPPULSE_APP_CLICK]: undefined,
  [RegAnalyticsEvent.EXIT_POP_UP_OPEN]: () =>
    logCustomEvent('Reg_ExitPopUp_Open'),
  [RegAnalyticsEvent.EXIT_POP_UP_LOG_OUT_CLICK]: () =>
    logCustomEvent('Reg_ExitPopUp_LogOutClick'),
  [RegAnalyticsEvent.EXIT_POP_UP_CONTINUE_SETUP_CLICK]: () =>
    logCustomEvent('Reg_ExitPopUp_ContinueSetupClick'),
  [RegAnalyticsEvent.INCORRECT_ACCESS_TOKEN_OPEN]: undefined,
  [RegAnalyticsEvent.MARKETING_OPT_IN_SAVE]: () =>
    logCustomEvent('Reg_MarketingOptIn_Save'),
  [RegAnalyticsEvent.MARKETING_OPT_OUT_SAVE]: () =>
    logCustomEvent('Reg_MarketingOptOut_Save'),
  [RegAnalyticsEvent.JUMP_RIGHT_IN_CLICK]: () =>
    logCustomEvent('Reg_JumpRightIn_Click'),
  [RegAnalyticsEvent.UNABLE_TO_SAVE_DETAILS_ERROR_OPEN]: ({ errorMessage }) =>
    logCustomEvent('Reg_UnableToSaveDetailsError_Open', {
      errorMessage,
    }),
  [RegAnalyticsEvent.UNABLE_TO_SAVE_DETAILS_ERROR_LOGIN_CLICK]: () =>
    logCustomEvent('Reg_UnableToSaveDetailsError_LoginClick'),
  [RegAnalyticsEvent.UNABLE_TO_SAVE_DETAILS_ERROR_GO_BACK_CLICK]: () =>
    logCustomEvent('Reg_UnableToSaveDetailsError_GoBackClick'),
  [RegAnalyticsEvent.UNABLE_TO_VERIFY_EMAIL_CLOSE_CLICK]: undefined,
  [RegAnalyticsEvent.ADD_EMAIL_ADDRESS_OPEN]: () =>
    logCustomEvent('Reg_AddEmailAddress_Open'),
  [RegAnalyticsEvent.ADD_EMAIL_ADDRESS_EXIT_CLICK]: () =>
    logCustomEvent('Reg_AddEmailAddress_ExitClick'),
  [RegAnalyticsEvent.ADD_EMAIL_ADDRESS_SEND_VERIF_CLICK]: () =>
    logCustomEvent('Reg_AddEmailAddress_SendVerifClick'),
  [RegAnalyticsEvent.EMAIL_ADDRESS_VERIFIED_OPEN]: () =>
    logCustomEvent('Reg_EmailAddressVerified_Open'),
  [RegAnalyticsEvent.EMAIL_ADDRESS_VERIFIED_CONTINUE_CLICK]: () =>
    logCustomEvent('Reg_EmailAddressVerified_ContinueClick'),
  [RegAnalyticsEvent.EMAIL_VER_SCREEN_CHANGE_EMAIL_CLICK]: () =>
    logCustomEvent('Reg_EmailVerScreen_ChangeEmailClick'),
  [RegAnalyticsEvent.EMAIL_VER_SCREEN_VERIFIED_EMAIL_CLICK]: () =>
    logCustomEvent('Reg_EmailVerScreen_VerifiedEmailClick'),
  [RegAnalyticsEvent.UNABLE_TO_VERIFY_EMAIL_OPEN]: ({ errorMessage }) =>
    logCustomEvent('Reg_UnableToVerifyEmail_Open', { errorMessage }),
  [RegAnalyticsEvent.UPDATE_EMAIL_ADDRESS_CLOSE_CLICK]: undefined,
  [RegAnalyticsEvent.UPDATE_EMAIL_ADDRESS_OPEN]: undefined,
  [RegAnalyticsEvent.UPDATE_EMAIL_ADDRESS_SEND_VERIF_CLICK]: undefined,
  [RegAnalyticsEvent.SETTING_UP_EVACCOUNT_OPEN]: () =>
    logCustomEvent('Reg_SettingUpEVAccount_Open'),
  [RegAnalyticsEvent.SETTING_UP_EV_ACCOUNT_LOGOUT_CLICK]: () =>
    logCustomEvent('Reg_SettingupEVAccount_LogoutClick'),
  [RegAnalyticsEvent.SETTING_UP_EV_ACCOUNT_TRY_AGAIN_CLICK]: () =>
    logCustomEvent('Reg_SettingUpEVAccount_TryAgainClick'),
  [RegAnalyticsEvent.TRY_AGAIN_CLICK]: undefined,
  [RegAnalyticsEvent.VERIFICATION_LINK_SENT_EXIT_CLICK]: undefined,
  [RegAnalyticsEvent.VERIFICATION_LINK_SENT_OPEN]: undefined,
  [RegAnalyticsEvent.EV_ACCOUNT_SETUP_SUCCESSFUL]: () =>
    logCustomEvent('Reg_EVAccountSetup_Successful'),
  [RegAnalyticsEvent.EV_ACCOUNT_SETUP_UNSUCCESSFUL]: () =>
    logCustomEvent('Reg_EVAccountSetup_Unsuccessful'),
  [FavouritesAnalyticsEvent.GET_FAVOURITES_SUCCESS]: () => {
    return logCustomEvent('Favourites_GetFavouriteSuccess');
  },
  [FavouritesAnalyticsEvent.GET_FAVOURITES_FAILED]: () => {
    return logCustomEvent('Favourites_GetFavouriteFailed');
  },
  [FavouritesAnalyticsEvent.ADD_FAVOURITE_SUCCESS]: params => {
    const site = params.site as Site;

    return logCustomEvent('Favourites_AddFavouriteSuccess', {
      Site_ID: site?.siteId,
      Site_Provider: site?.provider,
      Site_Country: site?.siteDetails?.country,
      CP_Scheme: site?.chargepoints
        ?.flatMap((c: { schemes: Array<{ schemeName: string }> }) => c.schemes)
        .map((s: { schemeName: string }) => s.schemeName)
        .join(','),
      CP_Operator: site?.cpo,
    });
  },
  [FavouritesAnalyticsEvent.ADD_FAVOURITE_FAILED]: params => {
    const site = params.site as Site;

    return logCustomEvent('Favourites_AddFavouriteFailed', {
      Site_ID: site?.siteId,
      Site_Provider: site?.provider,
      Site_Country: site?.siteDetails?.country,
      CP_Scheme: site?.chargepoints
        ?.flatMap((c: { schemes: Array<{ schemeName: string }> }) => c.schemes)
        .map((s: { schemeName: string }) => s.schemeName)
        .join(','),
      CP_Operator: site?.cpo,
    });
  },
  [FavouritesAnalyticsEvent.REMOVE_FAVOURITE_SUCCESS]: params => {
    const site = params.site as Site;

    return logCustomEvent('Favourites_RemoveFavouriteSuccess', {
      Site_ID: site?.siteId,
      Site_Provider: site?.provider,
      Site_Country: site?.siteDetails?.country,
      CP_Scheme: site?.chargepoints
        ?.flatMap((c: { schemes: Array<{ schemeName: string }> }) => c.schemes)
        .map((s: { schemeName: string }) => s.schemeName)
        .join(','),
      CP_Operator: site?.cpo,
    });
  },
  [FavouritesAnalyticsEvent.REMOVE_FAVOURITE_FAILED]: params => {
    const site = params.site as Site;

    return logCustomEvent('Favourites_RemoveFavouriteFailed', {
      Site_ID: site?.siteId,
      Site_Provider: site?.provider,
      Site_Country: site?.siteDetails?.country,
      CP_Scheme: site?.chargepoints
        ?.flatMap((c: { schemes: Array<{ schemeName: string }> }) => c.schemes)
        .map((s: { schemeName: string }) => s.schemeName)
        .join(','),
      CP_Operator: site?.cpo,
    });
  },
  [OnboardingAnalyticsEvent.REQUEST_SUCCESSFUL]: ({ providers }) => {
    return logCustomEvent('Onboarding_Request_Successful', { providers });
  },
  [OnboardingAnalyticsEvent.REQUEST_UNSUCCESSFUL]: ({ error, providers }) => {
    return logCustomEvent('Onboarding_Request_Unsuccessful', {
      error,
      providers,
    });
  },
  [OnboardingAnalyticsEvent.ONBOARDING_ACCOUNT_SUCCESSFUL]: () => {
    return logCustomEvent('Onboarding_Account_Request_Successful');
  },
  [OnboardingAnalyticsEvent.ONBOARDING_ACCOUNT_UNSUCCESSFUL]: ({ error }) => {
    return logCustomEvent('Onboarding_Account_Request_Unsuccessful', {
      error,
    });
  },
  [OnboardingAnalyticsEvent.ONBOARDING_CHARGING_SUCCESSFUL]: () => {
    return logCustomEvent('Onboarding_Charging_Request_Successful');
  },
  [OnboardingAnalyticsEvent.ONBOARDING_CHARGING_UNSUCCESSFUL]: ({ error }) => {
    return logCustomEvent('Onboarding_Charging_Request_Unsuccessful', {
      error,
    });
  },
  [OnboardingAnalyticsEvent.ONBOARDING_ROAMING_SUCCESSFUL]: () => {
    return logCustomEvent('Onboarding_Roaming_Request_Successful');
  },
  [OnboardingAnalyticsEvent.ONBOARDING_ROAMING_UNSUCCESSFUL]: ({ error }) => {
    return logCustomEvent('Onboarding_Roaming_Request_Unsuccessful', {
      error,
    });
  },

  [PartnerDriverAnalyticsEvent.UBER_UNLINK_ACCOUNTS_MODAL_OPEN]: ({
    user_journey,
  }) => logCustomEvent('UnlinkAccountsModal_Open', { user_journey }),
  [PartnerDriverAnalyticsEvent.UBER_UNLINK_ACCOUNTS_KEEP_UBER_CLICK]: ({
    user_journey,
  }) => logCustomEvent('UnlinkAccounts_KeepUber_Click', { user_journey }),
  [PartnerDriverAnalyticsEvent.UBER_UNLINK_ACCOUNTS_DISCONNECT_ACCOUNTS_CLICK]:
    ({ user_journey }) =>
      logCustomEvent('UnlinkAccounts_DisconnectAccounts_Click', {
        user_journey,
      }),
  [PartnerDriverAnalyticsEvent.UBER_UNLINK_ACCOUNTS_OPEN]: ({ user_journey }) =>
    logCustomEvent('UnlinkingAccounts_Open', { user_journey }),
  [PartnerDriverAnalyticsEvent.UBER_UNLINK_UNSUCCESSFULL_EVENT]: ({
    user_journey,
  }) => logCustomEvent('UnlinkingUnsuccessful_Event', { user_journey }),
  [PartnerDriverAnalyticsEvent.UBER_UNLINK_UNSUCCESSFULL_OPEN]: ({
    user_journey,
  }) => logCustomEvent('UnlinkingUnsuccessful_Open', { user_journey }),
  [PartnerDriverAnalyticsEvent.UBER_UNLINK_UNSUCCESSFULL_TRY_AGAIN_CLICK]: ({
    user_journey,
  }) =>
    logCustomEvent('UnlinkingUnsuccessful_TryAgain_Click', { user_journey }),
  [PartnerDriverAnalyticsEvent.UBER_UNLINK_UNSUCCESSFULL_CONTACT_CLICK]: ({
    user_journey,
  }) => logCustomEvent('UnlinkingUnsuccessful_Contact_Click', { user_journey }),
  [PartnerDriverAnalyticsEvent.UBER_UNLINK_ACCOUNTS_SUCCESSFULL_OPEN]: ({
    user_journey,
  }) => logCustomEvent('UnlinkingAccountsSuccessful_Open', { user_journey }),

  [UberAnalyticsEvent.WELCOME_TO_UBER_OPEN]: () =>
    logCustomEvent('WelcomeToUber_Open'),
  [UberAnalyticsEvent.WELCOME_TO_UBER_FIND_NEARBY_CHARGER_CLICK]: () =>
    logCustomEvent('FindNearbyCharger_Click'),
  [UberAnalyticsEvent.WELCOME_TO_UBER_VIEW_REWARDS_CLICK]: () =>
    logCustomEvent('WelcomeToUber_ViewRewards_Click'),
  [UberAnalyticsEvent.REAUTH_UBER_LINK_EXPIRED_OPEN]: () =>
    logCustomEvent('ReAuth_UberLinkExpired_Open'),
  [UberAnalyticsEvent.REAUTH_UBER_LINK_EXPIRED_VERIFY_CLICK]: () =>
    logCustomEvent('ReAuth_UberLinkExpired_Verify_Click'),
  [UberAnalyticsEvent.REAUTH_UBER_LINK_EXPIRED_X_CLICK]: () =>
    logCustomEvent('ReAuth_UberLinkExpired_X_Click'),
  [UberAnalyticsEvent.LOGIN_WITH_UBER_SUCCESS]: () =>
    logCustomEvent('LoginWithUber_Success'),
  [UberAnalyticsEvent.UBER_UNLINK_ACCOUNT_SUCCESS_SUBSCRIBE_CLICK]: ({
    user_journey,
  }) =>
    logCustomEvent('UnlinkAccountsSuccess_Subscribe_Click', { user_journey }),
  [UberAnalyticsEvent.UBER_UNLINK_ACCOUNT_SUCCESS_FIND_CHARGER_CLICK]: ({
    user_journey,
  }) =>
    logCustomEvent('UnlinkAccountsSuccess_FindCharger_Click', { user_journey }),
  [UberAnalyticsEvent.UNLINK_ACCOUNTS_MODAL_OPEN]: ({ user_journey }) =>
    logCustomEvent('UnlinkAccountsModal_Open', { user_journey }),
  [UberAnalyticsEvent.UNLINK_ACCOUNTS_KEEP_UBER_CLICK]: ({ user_journey }) =>
    logCustomEvent('UnlinkAccounts_KeepUber_Click', { user_journey }),
  [UberAnalyticsEvent.UNLINK_ACCOUNTS_DISCONNECT_ACCOUNTS_CLICK]: ({
    user_journey,
  }) =>
    logCustomEvent('UnlinkAccounts_DisconnectAccounts_Click', { user_journey }),
  [UberAnalyticsEvent.UNLINKING_UNSUCCESSFUL_EVENT]: ({ user_journey }) =>
    logCustomEvent('UnlinkingUnsuccessful_Event', { user_journey }),
  [UberAnalyticsEvent.UNLINKING_UNSUCCESSFUL_OPEN]: ({ user_journey }) =>
    logCustomEvent('UnlinkingUnsuccessful_Open', { user_journey }),
  [UberAnalyticsEvent.UNLINKING_UNSUCCESSFUL_CONTACT_CLICK]: ({
    user_journey,
  }) => logCustomEvent('UnlinkingUnsuccessful_Contact_Click', { user_journey }),
  [UberAnalyticsEvent.UNLINKING_UNSUCCESSFUL_TRY_AGAIN_CLICK]: ({
    user_journey,
  }) =>
    logCustomEvent('UnlinkingUnsuccessful_TryAgain_Click', { user_journey }),
  [UberAnalyticsEvent.UNLINKING_ACCOUNTS_OPEN]: ({ user_journey }) =>
    logCustomEvent('UnlinkingAccounts_Open', { user_journey }),
  [UberAnalyticsEvent.UNLINING_ACCOUNTS_SUCCESSFUL_OPEN]: ({ user_journey }) =>
    logCustomEvent('UnlinkingAccountsSuccessful_Open', { user_journey }),
  [UberAnalyticsEvent.CONNECTING_ACCOUNTS_SUCCESSFUL_OPEN]: () =>
    logCustomEvent('ConnectingAccountsSuccessful_Open'),
  [UberAnalyticsEvent.UBER_INELIGIBLE_ACCOUNT_OPEN]: () =>
    logCustomEvent('UberIneligibleAccount_Open'),
  [UberAnalyticsEvent.UBER_INELIGIBLE_ACCOUNT_TRYAGAIN_CLICK]: () =>
    logCustomEvent('UberIneligibleAccount_TryAgain_Click'),
  [UberAnalyticsEvent.UBER_INELIGIBLE_ACCOUNT_UNLINK_CLICK]: () =>
    logCustomEvent('UberIneligibleAccount_Unlink_Click'),
  [UberAnalyticsEvent.UBER_INELIGIBLE_ACCOUNT_RETRYLIMIT_OPEN]: () =>
    logCustomEvent('UberIneligibleAccount_RetryLimit_Open'),
  [UberAnalyticsEvent.UBER_INELIGIBLE_ACCOUNT_RETRY_SUCCESS]: () =>
    logCustomEvent('UberIneligibleAccount_Retry_Success'),
  [UberMigrationAnalyticsEvent.UBER_MIGRATION_SCREEN_OPEN]: () =>
    logCustomEvent('UberMigrationScreen_Open'),
  [UberMigrationAnalyticsEvent.UBER_MIGRATION_SCREEN_MOVE_TO_SUBS_CLICK]: () =>
    logCustomEvent('UberMigrationScreen_MoveToSubs_Click'),
  [UberMigrationAnalyticsEvent.UBER_MIGRATION_SCREEN_UPDATE_ACCOUNT_CLICK]:
    () => logCustomEvent('UberMigrationScreen_UpdateAccount_Click'),
  [UberMigrationAnalyticsEvent.UBER_MIGRATION_SCREEN_DISMISS_CLICK]: () =>
    logCustomEvent('UberMigrationScreen_Dismiss_Click'),

  [MigrationFlowAnalyticsEvent.PAYG_MIGRATION_SCREEN_OPEN]: ({
    migration_status,
  }) => logCustomEvent('PAYGMigrationScreen_Open', { migration_status }),
  [MigrationFlowAnalyticsEvent.PAYG_MIGRATIONSCREEN_DISMISS_CLICK]: ({
    migration_status,
  }) =>
    logCustomEvent('PAYGMigrationScreen_Dismiss_Click', { migration_status }),
  [MigrationFlowAnalyticsEvent.PAYG_MIGRATIONSCREEN_UPDATEACCOUNT_CLICK]: ({
    entered_value,
    migration_status,
  }) =>
    logCustomEvent('PAYGMigrationScreen_UpdateAccount_Click', {
      entered_value,
      migration_status,
    }),
  [MigrationFlowAnalyticsEvent.PAYG_MIGRATIONSCREEN_ORDER_RFID_CLICK]: () =>
    logCustomEvent('PAYGMigrationScreen_OrderRFID_Click'),
  [MigrationFlowAnalyticsEvent.PAYG_MIGRATIONSCREEN_MOVETOSUBS_CLICK]: () =>
    logCustomEvent('PAYGMigrationScreen_MoveToSUBS_Click'),
  [MigrationFlowAnalyticsEvent.PAYG_MIGRATIONSCREEN_FAQ_CLICK]: ({
    migration_status,
  }) => logCustomEvent('PAYGMigrationScreen_FAQ_Click', { migration_status }),
  [MigrationFlowAnalyticsEvent.PAYG_MIGRATION_ERRORSCREEN_OPEN]: ({
    error,
    migration_status,
  }) =>
    logCustomEvent('PAYGMigration_ErrorScreen_Open', {
      error,
      migration_status,
    }),

  [MigrationFlowAnalyticsEvent.SUBS_MIGRATION_SCREEN_OPEN]: ({
    migration_status,
  }) => logCustomEvent('SUBSMigrationScreen_Open', { migration_status }),
  [MigrationFlowAnalyticsEvent.SUBS_MIGRATIONSCREEN_DISMISS_CLICK]: ({
    migration_status,
  }) =>
    logCustomEvent('SUBSMigrationScreen_Dismiss_Click', { migration_status }),
  [MigrationFlowAnalyticsEvent.SUBS_MIGRATIONSCREEN_UPDATEACCOUNT_CLICK]: ({
    entered_value,
    migration_status,
  }) =>
    logCustomEvent('SUBSMigrationScreen_UpdateAccount_Click', {
      entered_value,
      migration_status,
    }),
  [MigrationFlowAnalyticsEvent.SUBS_MIGRATIONSCREEN_KEEPSUBS_CLICK]: () =>
    logCustomEvent('SUBSMigrationScreen_KeepSubs_Click'),
  [MigrationFlowAnalyticsEvent.SUBS_MIGRATIONSCREEN_DECLINESUBS_CLICK]: () =>
    logCustomEvent('SUBSMigrationScreen_DeclineSubs_Click'),
  [MigrationFlowAnalyticsEvent.SUBS_MIGRATIONSCREEN_FAQ_CLICK]: ({
    migration_status,
  }) => logCustomEvent('SUBSMigrationScreen_FAQ_Click', { migration_status }),
  [MigrationFlowAnalyticsEvent.SUBS_MIGRATION_ERRORSCREEN_OPEN]: ({
    error,
    migration_status,
  }) =>
    logCustomEvent('SUBSMigration_ErrorScreen_Open', {
      error,
      migration_status,
    }),

  [SiteBannerAnalyticsEvent.CHARGE_SELECT_CONNECTOR_UNENTITLED]: params =>
    logCustomEvent('Charge_SelectConnector_Unentitled', {
      Site_Provider: params?.siteDetails?.siteProvider,
      Site_Country: params?.siteDetails?.siteCountry,
      CP_Scheme: params?.siteDetails?.cpScheme,
      CP_Operator: params?.siteDetails?.cpOperator,
      Connector_Type: params.connectorType,
      Banner_Type: params.bannerType,
      CP_ID: params.cpId,
    }),

  [SiteBannerAnalyticsEvent.MAP_SITE_UNENTITLED]: params =>
    logCustomEvent('Map_Site_Unentitled', {
      Site_ID: params?.siteDetails?.siteId,
      Site_Provider: params?.siteDetails?.siteProvider,
      Site_Country: params?.siteDetails?.siteCountry,
      CP_Scheme: params?.siteDetails?.cpScheme,
      CP_Operator: params?.siteDetails?.cpOperator,
      Connector_Type: params.connectorType,
      Banner_Type: params.bannerType,
      isLocationServicesEnabled: params.isLocationServiceEnabled,
    }),
  [SubsAnalyticsEvent.SUBS_CANCEL_SUBSCRIPTION_FAILED]: ({ error_message }) => {
    return logCustomEvent('SUBS_CancelSubscription_Failed', {
      ErrorMessage: error_message,
    });
  },
  [ProfileAnalyticsEventType.OFFERS_WIDGET_CLICK]: undefined,
};

/**
 * Handles logging a custom analytics event to Firebase
 * @param {AnalyticsEventType} event an analytics event
 */
export const analyticsEvent = async (event: AnalyticsEventType) => {
  const command: Function | undefined = eventMap[event.type];
  if (command) {
    return command(event.payload);
  }
  return null;
};

// Returns the Firebase analytics instance
export const instance = () => analytics();

/**
 * Init firebase analytics, allow analytics consent to enable/disable collection of data
 */
export const init = (enabled: boolean) => {
  analytics().setAnalyticsCollectionEnabled(enabled);
};
