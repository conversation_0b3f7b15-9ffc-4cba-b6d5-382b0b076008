import { MapProviderProps } from '@bp/map-mfe';
import {
  PartnerDriverExternalLinks,
  PartnerDriverFeatureFlags,
} from '@bp/partnerdriver-mfe/dist/common/interfaces';
import { ToggledOffLanguages } from '@bp/profile-mfe';
import { AppCountry } from '@bp/registration-mfe/src/common/enums';

type MapFeatureFlags = Required<NonNullable<MapProviderProps['featureFlags']>>;

export type RemoteConfig = {
  app_shell: {
    enableSoftOnboarding: boolean;
    enable_customer_migration: boolean;
    es_joint_venture_number: string;
    es_joint_venture_operator: string;
    forced_update: {
      blocked_versions: Array<string>;
      minimum_version: string;
    };
    full_outage: {
      body: string;
      body_de_DE: string;
      body_en_GB: string;
      body_es_ES: string;
      body_fr_FR: string;
      body_nl_NL: string;
      body_pt_PT: string;
      enabled: boolean;
      heading: string;
      heading_de_DE: string;
      heading_en_GB: string;
      heading_es_ES: string;
      heading_fr_FR: string;
      heading_nl_NL: string;
      heading_pt_PT: string;
    };
    help_page_countries: Record<string, boolean>;
    minimum_credit?: number;
    partial_outage: {
      body: string;
      body_de_DE: string;
      body_en_GB: string;
      body_es_ES: string;
      body_fr_FR: string;
      body_nl_NL: string;
      body_pt_PT: string;
      enabled: boolean;
      heading: string;
      heading_de_DE: string;
      heading_en_GB: string;
      heading_es_ES: string;
      heading_fr_FR: string;
      heading_nl_NL: string;
      heading_pt_PT: string;
      id: string;
    };
  };
  charge_history_mfe: {
    activity_download_receipt_button: boolean;
    activity_tab: boolean;
    charges_screen: boolean;
    eroaming: boolean;
    invoices_download_receipt_button: boolean;
    invoices_tab: boolean;
    membership_button: boolean;
    pay_outstanding_balance: boolean;
    showUnitPrice: { BPCM: boolean; DCS: boolean; HTB: boolean };
  };
  charge_mfe: {
    enable_charge_banner: boolean;
    enable_charge_summary: boolean;
    enable_download_receipt: boolean;
    enable_ev_account_setup: boolean;
    enable_favourites: boolean;
    enable_favourites_login_modal: boolean;
    enable_guest_charge: boolean;
    enable_monitoring_preconditional_stop: boolean;
    enable_nearby_sites: boolean;
    enable_need_help: boolean;
    enable_pre_auth: boolean;
    enable_qr_code_scan: boolean;
    enable_roaming_charge: boolean;
    enable_search_torch: boolean;
    enable_serial_search: boolean;
    enable_start_charge: boolean;
    max_user_distance: number;
    preauth_display_value: number;
    show_new_summary_screen: boolean;
  };
  credit_mfe: {
    enable_stripe: boolean;
    enable_topup_credit: boolean;
  };
  external_links: {
    bp_support_phone_number: string;
    bppay_wallet_microsite_link: string;
    contact_us_link: string;
    de_pricing_link: string;
    de_webshop_link: string;
    emsp_charges_link: string;
    emsp_faq_link: string;
    emsp_privacy_policy_link: string;
    emsp_tcs_link: string;
    emsp_tcs_link_v1: string;
    emsp_uber_microsite_link: string;
    emsp_uber_reauth_link: string;
    emsp_view_price_link: string;
    help_request_form_link: string;
    marketing_website_link?: string;
    tariff_pricing_link: string;
    uber_offering_link: string;
    uber_support_link: string;
  } & PartnerDriverExternalLinks;
  favourites_mfe: {};
  fetched: boolean;
  guest_mfe: {};
  map_mfe: MapFeatureFlags;
  map_mfe_config: {
    availableCountries: Record<string, boolean>;
    availableProviders: Record<string, boolean>;
    guestChargeProviders: Record<string, boolean>;
    guestPriceProviders: Record<string, boolean>;
    initialRegion: {
      latitude: number;
      latitudeDelta: number;
      longitude: number;
      longitudeDelta: number;
    };
  };
  offers_mfe: {};
  partner_driver_mfe: PartnerDriverFeatureFlags;
  profile_mfe: {
    autocomplete_address_enabled: boolean;
    credit: boolean;
    deleteAccount: boolean;
    editEmailAddress: boolean;
    editMobileNumber: boolean;
    editProfile: boolean;
    editUserFullName: boolean;
    feedback: boolean;
    goPasswordless: boolean;
    language: boolean;
    loginButton: boolean;
    logout: boolean;
    marketing: boolean;
    offers: boolean;
    partnerDriver: boolean;
    partnerDriverNavigation: boolean;
    passwordChange: boolean;
    paymentDetails: boolean;
    pricing: boolean;
    privacy: boolean;
    pulseMembership: boolean;
    recentTransactions: boolean;
    settings: boolean;
    subscription: boolean;
    terms: boolean;
    toggleDELanguage: boolean;
    toggledOffLanguages: ToggledOffLanguages;
    uberPartnerDriver: boolean;
    uberPartnerDriverNavigation: boolean;
    userDetails: boolean;
  };
  registration_mfe: {
    enableAralActivationMessage: boolean;
  };
  rfid_mfe: {
    autocomplete_address_enabled: boolean;
    benefits_show_card_arrival_text: boolean;
    benefits_show_usable_across_network_text: boolean;
    card_arrival_duration_in_days: string;
    enable_rfid: boolean;
    max_card_arrival_duration_in_days: string;
    roaming_enabled: boolean;
    uberOfferValidityDate: string;
  };
  rtbf_mfe: {};
  subscription_mfe: {
    block_subscription_upgrade_during_migration: boolean;
    disableIntroOffer: boolean;
    enableInvoicesList: boolean;
    enableOfferDetails: boolean;
    enableRFID: boolean;
    extraTariffDiscountPercentage: number;
    introOfferCredit: number;
    introOfferCreditDuration: number;
    introPercentageDiscount: number;
    pollingTimeout: number;
    savingRate: number;
    'subsChargeRateAC<22kW': number;
    'subsChargeRateDC<50kW': number;
    'subsChargeRateDC>50kW': number;
    subsChargekWh: string;
    subsChargingRatesPromo: number;
    subsDrivingDistancekm: string;
    subscriptionAmount: number;
  };
  wallet_mfe: {};
};

type BrandCountry = { icon: string; type: AppCountry };

export type BrandCountryConfig = { brandCountries: Array<BrandCountry> };

export type PulseAppConfig = RemoteConfig & BrandCountryConfig;
