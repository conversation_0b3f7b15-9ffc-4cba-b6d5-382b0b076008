import { RemoteConfig } from '@config/config.types';

export const DEFAULT_VALUES_NL: RemoteConfig = {
  app_shell: {
    enable_customer_migration: false,
    enableSoftOnboarding: false,
    es_joint_venture_number: '+34 900 999 199',
    es_joint_venture_operator: 'Iberdrola | bp pulse',
    forced_update: {
      blocked_versions: ['2.2.6', '2.2.7'],
      minimum_version: '3.14.0',
    },
    full_outage: {
      body: 'string',
      body_de_DE: 'string',
      body_en_GB: 'string',
      body_es_ES: 'string',
      body_fr_FR: 'string',
      body_nl_NL: 'string',
      body_pt_PT: 'string',
      enabled: false,
      heading: 'string',
      heading_de_DE: 'string',
      heading_en_GB: 'string',
      heading_es_ES: 'string',
      heading_fr_FR: 'string',
      heading_nl_NL: 'string',
      heading_pt_PT: 'string',
    },
    help_page_countries: {
      de: false,
      es: true,
      nl: true,
      uk: true,
    },
    partial_outage: {
      body: 'string',
      body_de_DE: 'string',
      body_en_GB: 'string',
      body_es_ES: 'string',
      body_fr_FR: 'string',
      body_nl_NL: 'string',
      body_pt_PT: 'string',
      enabled: false,
      heading: 'string',
      heading_de_DE: 'string',
      heading_en_GB: 'string',
      heading_es_ES: 'string',
      heading_fr_FR: 'string',
      heading_nl_NL: 'string',
      heading_pt_PT: 'string',
      id: 'string',
    },
  },
  charge_history_mfe: {
    activity_download_receipt_button: true,
    activity_tab: true,
    charges_screen: true,
    eroaming: false,
    invoices_download_receipt_button: false,
    invoices_tab: false,
    membership_button: false,
    pay_outstanding_balance: true,
    showUnitPrice: { BPCM: true, DCS: false, HTB: false },
  },
  charge_mfe: {
    enable_charge_banner: true,
    enable_charge_summary: true,
    enable_download_receipt: false,
    enable_ev_account_setup: false,
    enable_favourites: true,
    enable_favourites_login_modal: false,
    enable_guest_charge: false,
    enable_monitoring_preconditional_stop: false,
    enable_nearby_sites: false,
    enable_need_help: false,
    enable_pre_auth: false,
    enable_qr_code_scan: false,
    enable_roaming_charge: false,
    enable_search_torch: false,
    enable_serial_search: true,
    enable_start_charge: true,
    max_user_distance: 2000,
    preauth_display_value: 1,
    show_new_summary_screen: false,
  },
  credit_mfe: {
    enable_stripe: false,
    enable_topup_credit: false,
  },
  external_links: {
    adac_charge_link: 'https://www.adac.de/e-charge',
    adac_tcs_link:
      'https://www.aral.de/de/global/retail/pulse/emsp-adac-echarge-tou.html',
    bp_support_phone_number: '',
    bppay_wallet_microsite_link: 'https://bppay-wallet-web-app.bp.com',
    contact_us_link: 'https://www.bppulse.co.uk/help-centre/contact',
    de_pricing_link: '',
    de_webshop_link: 'https://pay.bppulse.com',
    emsp_charges_link: '',
    emsp_faq_link: '',
    emsp_privacy_policy_link:
      ' https://www.bp.com/nl_nl/netherlands/home/<USER>/pulse/algemene-voorwaarden-en-privacy.html',
    emsp_tcs_link:
      ' https://www.bp.com/nl_nl/netherlands/home/<USER>/pulse/algemene-voorwaarden-en-privacy.html',
    emsp_tcs_link_v1: '',
    emsp_uber_microsite_link: '',
    emsp_uber_reauth_link: 'https://reward.bppulse.com/uber-pro/reauth',
    emsp_view_price_link: '',
    help_centre_contact_link: '',
    help_request_form_link:
      'https://www.bp.com/nl_nl/netherlands/home/<USER>/pulse/Contact.html',
    marketing_website_link: 'https://www.bppulse.nl/uber',
    rewards_with_uber_link: '',
    tariff_pricing_link: '',
    uber_offering_link: 'https://network.bppulse.co.uk/uber-rewards/',
    uber_rewards_tcs_link:
      'https://www.bppulse.co.uk/uber-rewards-terms-and-conditions',
    uber_support_link: '',
  },
  favourites_mfe: {},
  fetched: false,
  guest_mfe: {},
  map_mfe: {
    enable24HFilter: true,
    enableB2BFeatures: false,
    enableChargeFlow: true,
    enableFavourites: true,
    enableOperatorFilter: false,
    enableOtherNetworksFilter: false,
    enablePriceFilter: false,
    enableShareButton: false,
    enableSiteDetailsTab: true,
    enableSiteFuelsTab: false,
    enableUberFilter: false,
  },
  map_mfe_config: {
    availableCountries: {
      DE: true,
      ES: true,
      NL: true,
      UK: true,
    },
    availableProviders: {
      aral: true,
      bpcm: true,
      dcs: true,
      semarchy: true,
    },
    guestChargeProviders: {
      hastobe: true,
    },
    guestPriceProviders: {},
    initialRegion: {
      latitude: 52.370216,
      latitudeDelta: 3,
      longitude: 4.895168,
      longitudeDelta: 2,
    },
  },
  offers_mfe: {},
  partner_driver_mfe: {
    card_arrival_duration_in_days: '',
    enableUnlinkPartnerAccount: false,
    isMigrating: false,
    uberOfferValidityDate: '',
    uberTariffPerKw: '',
  },
  profile_mfe: {
    autocomplete_address_enabled: true,
    credit: false,
    deleteAccount: true,
    editEmailAddress: false,
    editMobileNumber: false,
    editProfile: false,
    editUserFullName: false,
    feedback: false,
    goPasswordless: false,
    language: true,
    loginButton: false,
    logout: true,
    marketing: true,
    offers: false,
    partnerDriver: false,
    partnerDriverNavigation: false,
    passwordChange: false,
    paymentDetails: true,
    pricing: false,
    privacy: true,
    pulseMembership: false,
    recentTransactions: true,
    settings: true,
    subscription: false,
    terms: true,
    toggleDELanguage: false,
    toggledOffLanguages: {
      DE: true,
      FR: true,
    },
    uberPartnerDriver: false,
    uberPartnerDriverNavigation: false,
    userDetails: true,
  },
  registration_mfe: {
    enableAralActivationMessage: false,
  },
  rfid_mfe: {
    autocomplete_address_enabled: false,
    benefits_show_card_arrival_text: false,
    benefits_show_usable_across_network_text: false,
    card_arrival_duration_in_days: '7',
    enable_rfid: false,
    max_card_arrival_duration_in_days: '14',
    roaming_enabled: false,
    uberOfferValidityDate: '',
  },
  rtbf_mfe: {},
  subscription_mfe: {
    block_subscription_upgrade_during_migration: false,
    disableIntroOffer: false,
    enableInvoicesList: false,
    enableOfferDetails: false,
    enableRFID: false,
    extraTariffDiscountPercentage: 0,
    introOfferCredit: 0,
    introOfferCreditDuration: 0,
    introPercentageDiscount: 20,
    pollingTimeout: 36000,
    savingRate: 55,
    'subsChargeRateAC<22kW': 0.42,
    'subsChargeRateDC<50kW': 0.46,
    'subsChargeRateDC>50kW': 0.54,
    subsChargekWh: '47',
    subsChargingRatesPromo: 0.44,
    subsDrivingDistancekm: '215',
    subscriptionAmount: 7.85,
  },
  wallet_mfe: {},
};
