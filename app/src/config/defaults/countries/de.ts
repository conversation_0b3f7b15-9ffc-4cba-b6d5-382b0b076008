import { RemoteConfig } from '@config/config.types';

export const DEFAULT_VALUES_DE: RemoteConfig = {
  app_shell: {
    enable_customer_migration: false,
    enableSoftOnboarding: false,
    es_joint_venture_number: '+34 900 999 199',
    es_joint_venture_operator: 'Iberdrola | bp pulse',
    forced_update: {
      blocked_versions: ['2.2.6', '2.2.7'],
      minimum_version: '3.14.0',
    },
    full_outage: {
      body: 'string',
      body_de_DE: 'string',
      body_en_GB: 'string',
      body_es_ES: 'string',
      body_fr_FR: 'string',
      body_nl_NL: 'string',
      body_pt_PT: 'string',
      enabled: false,
      heading: 'string',
      heading_de_DE: 'string',
      heading_en_GB: 'string',
      heading_es_ES: 'string',
      heading_fr_FR: 'string',
      heading_nl_NL: 'string',
      heading_pt_PT: 'string',
    },
    help_page_countries: {
      de: true,
      es: true,
      nl: true,
      uk: true,
    },
    partial_outage: {
      body: 'string',
      body_de_DE: 'string',
      body_en_GB: 'string',
      body_es_ES: 'string',
      body_fr_FR: 'string',
      body_nl_NL: 'string',
      body_pt_PT: 'string',
      enabled: false,
      heading: 'string',
      heading_de_DE: 'string',
      heading_en_GB: 'string',
      heading_es_ES: 'string',
      heading_fr_FR: 'string',
      heading_nl_NL: 'string',
      heading_pt_PT: 'string',
      id: 'string',
    },
  },
  charge_history_mfe: {
    activity_download_receipt_button: true,
    activity_tab: false,
    charges_screen: false,
    eroaming: false,
    invoices_download_receipt_button: false,
    invoices_tab: false,
    membership_button: false,
    pay_outstanding_balance: false,
    showUnitPrice: { BPCM: true, DCS: false, HTB: false },
  },
  charge_mfe: {
    enable_charge_banner: true,
    enable_charge_summary: true,
    enable_download_receipt: false,
    enable_ev_account_setup: false,
    enable_favourites: false,
    enable_favourites_login_modal: false,
    enable_guest_charge: false,
    enable_monitoring_preconditional_stop: false,
    enable_nearby_sites: false,
    enable_need_help: false,
    enable_pre_auth: false,
    enable_qr_code_scan: false,
    enable_roaming_charge: false,
    enable_search_torch: false,
    enable_serial_search: false,
    enable_start_charge: true,
    max_user_distance: 2000,
    preauth_display_value: 1,
    show_new_summary_screen: false,
  },
  credit_mfe: {
    enable_stripe: false,
    enable_topup_credit: false,
  },
  external_links: {
    adac_charge_link: 'https://www.adac.de/e-charge',
    adac_tcs_link:
      'https://www.aral.de/de/global/retail/pulse/emsp-adac-echarge-tou.html',
    bp_support_phone_number: '',
    bppay_wallet_microsite_link: 'https://bppay-wallet-web-app.bp.com',
    contact_us_link: 'https://www.bppulse.co.uk/help-centre/contact',
    de_pricing_link:
      'https://www.aral.de/de/global/retail/pulse/preisliste-emsp-angebot.html',
    de_webshop_link: 'https://pay.bppulse.com',
    emsp_charges_link: '',
    emsp_faq_link: '',
    emsp_privacy_policy_link:
      'https://www.aral.de/de/global/retail/pulse/emsp-dpp.html',
    emsp_tcs_link: 'https://www.aral.de/de/global/retail/pulse/emsp-agb.html',
    emsp_tcs_link_v1:
      'https://www.aral.de/de/global/retail/pulse/emsp-agb-v2.html',
    emsp_uber_microsite_link: '',
    emsp_uber_reauth_link: '',
    emsp_view_price_link: '',
    help_centre_contact_link: '',
    help_request_form_link: '',
    rewards_with_uber_link: '',
    tariff_pricing_link: '',
    uber_offering_link: '',
    uber_rewards_tcs_link: '',
    uber_support_link: '',
  },
  favourites_mfe: {},
  fetched: false,
  guest_mfe: {},
  map_mfe: {
    enable24HFilter: false,
    enableB2BFeatures: false,
    enableChargeFlow: false,
    enableFavourites: false,
    enableOperatorFilter: true,
    enableOtherNetworksFilter: true,
    enablePriceFilter: false,
    enableShareButton: false,
    enableSiteDetailsTab: false,
    enableSiteFuelsTab: false,
    enableUberFilter: false,
  },
  map_mfe_config: {
    availableCountries: {
      DE: true,
      ES: true,
      NL: true,
      UK: true,
    },
    availableProviders: {
      aral: true,
      bpcm: true,
      dcs: true,
      semarchy: true,
    },
    guestChargeProviders: {
      hastobe: true,
    },
    guestPriceProviders: {},
    initialRegion: {
      latitude: 52.52,
      latitudeDelta: 3,
      longitude: 13.405,
      longitudeDelta: 2,
    },
  },
  offers_mfe: {},
  partner_driver_mfe: {
    card_arrival_duration_in_days: '',
    enableUnlinkPartnerAccount: false,
    isMigrating: false,
    uberOfferValidityDate: '',
    uberTariffPerKw: '',
  },
  profile_mfe: {
    autocomplete_address_enabled: true,
    credit: false,
    deleteAccount: false,
    editEmailAddress: false,
    editMobileNumber: false,
    editProfile: false,
    editUserFullName: false,
    feedback: false,
    goPasswordless: false,
    language: true,
    loginButton: false,
    logout: true,
    marketing: true,
    offers: false,
    partnerDriver: false,
    partnerDriverNavigation: false,
    passwordChange: false,
    paymentDetails: true,
    pricing: true,
    privacy: true,
    pulseMembership: false,
    recentTransactions: true,
    settings: true,
    subscription: false,
    terms: true,
    toggleDELanguage: true,
    toggledOffLanguages: {
      FR: true,
    },
    uberPartnerDriver: false,
    uberPartnerDriverNavigation: false,
    userDetails: true,
  },
  registration_mfe: {
    enableAralActivationMessage: true,
  },
  rfid_mfe: {
    autocomplete_address_enabled: false,
    benefits_show_card_arrival_text: true,
    benefits_show_usable_across_network_text: true,
    card_arrival_duration_in_days: '7',
    enable_rfid: false,
    max_card_arrival_duration_in_days: '14',
    roaming_enabled: false,
    uberOfferValidityDate: '',
  },
  rtbf_mfe: {},
  subscription_mfe: {
    block_subscription_upgrade_during_migration: false,
    disableIntroOffer: false,
    enableInvoicesList: false,
    enableOfferDetails: false,
    enableRFID: false,
    extraTariffDiscountPercentage: 10,
    introOfferCredit: 45,
    introOfferCreditDuration: 5,
    introPercentageDiscount: 10,
    pollingTimeout: 36000,
    savingRate: 55,
    'subsChargeRateAC<22kW': 0.42,
    'subsChargeRateDC<50kW': 0.46,
    'subsChargeRateDC>50kW': 0.54,
    subsChargekWh: '20',
    subsChargingRatesPromo: 0.54,
    subsDrivingDistancekm: '100+',
    subscriptionAmount: 2.99,
  },
  wallet_mfe: {},
};
